# Development Environment Configuration
REACT_APP_API_URL=http://localhost:8000/api
REACT_APP_WS_URL=ws://localhost:8000
REACT_APP_GOOGLE_MAPS_API_KEY=your-development-google-maps-api-key
PORT=3001
BROWSER=none

# Clerk Authentication Configuration - Development
REACT_APP_CLERK_PUBLISHABLE_KEY=pk_test_YWxlcnQtY2F0ZmlzaC00Ny5jbGVyay5hY2NvdW50cy5kZXYk
REACT_APP_CLERK_SIGN_IN_URL=/sign-in
REACT_APP_CLERK_SIGN_UP_URL=/sign-up
REACT_APP_CLERK_AFTER_SIGN_IN_URL=/dashboard
REACT_APP_CLERK_AFTER_SIGN_UP_URL=/dashboard

# Development specific settings
REACT_APP_DEBUG=true
REACT_APP_LOG_LEVEL=debug
