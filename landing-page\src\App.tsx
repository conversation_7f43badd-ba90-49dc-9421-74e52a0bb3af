import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ClerkProvider } from '@clerk/clerk-react';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import HomePage from './pages/HomePage';
import AdminLoginPage from './pages/AdminLoginPage';
import ClientLoginPage from './pages/ClientLoginPage';
import ClientSignupPage from './pages/ClientSignupPage';
import AdminRedirect from './components/AdminRedirect';
import ClientRedirect from './components/ClientRedirect';

// Get Clerk publishable key from environment
const clerkPubKey = process.env.REACT_APP_CLERK_PUBLISHABLE_KEY || 'pk_test_YWxlcnQtY2F0ZmlzaC00Ny5jbGVyay5hY2NvdW50cy5kZXYk';

console.log('Clerk Publishable Key:', clerkPubKey ? 'Present' : 'Missing');

if (!clerkPubKey) {
  console.error('Missing Clerk Publishable Key');
}

// Create Material-UI theme
const theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
  },
});

function App() {
  return (
    <ClerkProvider publishableKey={clerkPubKey}>
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <Router>
          <div className="App">
            <Routes>
              <Route path="/" element={<HomePage />} />
              <Route path="/admin/login" element={<AdminLoginPage />} />
              <Route path="/admin/dashboard" element={<AdminRedirect />} />
              <Route path="/admin/*" element={<AdminRedirect />} />
              <Route path="/client/login" element={<ClientLoginPage />} />
              <Route path="/client/signup" element={<ClientSignupPage />} />
              <Route path="/client/dashboard" element={<ClientRedirect />} />
              <Route path="/client/*" element={<ClientRedirect />} />
            </Routes>
          </div>
        </Router>
      </ThemeProvider>
    </ClerkProvider>
  );
}


export default App;