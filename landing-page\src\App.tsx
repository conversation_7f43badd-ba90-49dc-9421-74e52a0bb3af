import React from 'react';
import './App.css';

function App() {
  const handleAdminLogin = () => {
    window.location.href = 'http://localhost:3001';
  };

  const handleClientLogin = () => {
    window.location.href = 'http://localhost:3003';
  };

  return (
    <div className="App">
      <header className="hero-section">
        <nav className="navbar">
          <div className="nav-container">
            <div className="nav-logo">
              <h2>BahinLink</h2>
            </div>
            <div className="nav-links">
              <button onClick={handleAdminLogin} className="nav-btn admin-btn">
                Admin Portal
              </button>
              <button onClick={handleClientLogin} className="nav-btn client-btn">
                Client Portal
              </button>
            </div>
          </div>
        </nav>
        
        <div className="hero-content">
          <div className="hero-text">
            <h1>Workforce Management Made Simple</h1>
            <p className="hero-subtitle">
              Streamline your workforce operations with real-time tracking, 
              comprehensive reporting, and intelligent scheduling.
            </p>
            <div className="hero-buttons">
              <button onClick={handleAdminLogin} className="cta-btn primary">
                Access Admin Dashboard
              </button>
              <button onClick={handleClientLogin} className="cta-btn secondary">
                Client Portal
              </button>
            </div>
          </div>
          <div className="hero-visual">
            <div className="feature-card">
              <div className="feature-icon">📊</div>
              <h3>Real-time Analytics</h3>
              <p>Monitor workforce performance with live dashboards</p>
            </div>
            <div className="feature-card">
              <div className="feature-icon">📍</div>
              <h3>GPS Tracking</h3>
              <p>Track employee locations and optimize routes</p>
            </div>
            <div className="feature-card">
              <div className="feature-icon">⚡</div>
              <h3>Instant Updates</h3>
              <p>Get real-time notifications and status updates</p>
            </div>
          </div>
        </div>
      </header>

      <section className="features-section">
        <div className="container">
          <h2>Why Choose BahinLink?</h2>
          <div className="features-grid">
            <div className="feature-item">
              <div className="feature-icon-large">🎯</div>
              <h3>Precision Management</h3>
              <p>Advanced scheduling and resource allocation tools for maximum efficiency</p>
            </div>
            <div className="feature-item">
              <div className="feature-icon-large">🔒</div>
              <h3>Enterprise Security</h3>
              <p>Bank-level security with role-based access control and data encryption</p>
            </div>
            <div className="feature-item">
              <div className="feature-icon-large">📱</div>
              <h3>Mobile Ready</h3>
              <p>Full mobile support for on-the-go workforce management</p>
            </div>
          </div>
        </div>
      </section>

      <footer className="footer">
        <div className="container">
          <div className="footer-content">
            <div className="footer-section">
              <h3>BahinLink</h3>
              <p>Empowering businesses with intelligent workforce management solutions.</p>
            </div>
            <div className="footer-section">
              <h4>Quick Access</h4>
              <ul>
                <li><button onClick={handleAdminLogin} className="footer-link">Admin Dashboard</button></li>
                <li><button onClick={handleClientLogin} className="footer-link">Client Portal</button></li>
              </ul>
            </div>
            <div className="footer-section">
              <h4>Support</h4>
              <ul>
                <li><button className="footer-link">Documentation</button></li>
                <li><button className="footer-link">Help Center</button></li>
              </ul>
            </div>
          </div>
          <div className="footer-bottom">
            <p>&copy; 2024 BahinLink. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}

export default App;