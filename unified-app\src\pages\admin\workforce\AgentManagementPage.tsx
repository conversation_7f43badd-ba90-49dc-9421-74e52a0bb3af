import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Grid,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  Avatar,
  Fab,
  Alert,
  CircularProgress,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  Person as PersonIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  Badge as BadgeIcon,
} from '@mui/icons-material';
import { useAppSelector, useAppDispatch } from '../../../store/hooks';
import { setAgents, setLoading, setError } from '../../../store/slices/admin/agentsSlice';

interface Agent {
  id: string;
  employeeId: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  status: 'active' | 'inactive' | 'suspended';
  role: 'security_guard' | 'supervisor' | 'patrol_officer';
  hireDate: string;
  skills: string[];
  certifications: string[];
  currentSite?: string;
  emergencyContact: {
    name: string;
    phone: string;
    relationship: string;
  };
  performance: {
    rating: number;
    completedShifts: number;
    incidentReports: number;
  };
}

const AgentManagementPage: React.FC = () => {
  const dispatch = useAppDispatch();
  const { agents, loading, error } = useAppSelector(state => state.admin.agents);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedAgent, setSelectedAgent] = useState<Agent | null>(null);
  const [dialogMode, setDialogMode] = useState<'add' | 'edit' | 'view'>('add');

  useEffect(() => {
    loadAgents();
  }, []);

  const loadAgents = async () => {
    dispatch(setLoading(true));
    try {
      // Mock data - in real app, this would be an API call
      const mockAgents: Agent[] = [
        {
          id: '1',
          employeeId: 'EMP001',
          firstName: 'John',
          lastName: 'Smith',
          email: '<EMAIL>',
          phone: '+961 70 123 456',
          status: 'active',
          role: 'security_guard',
          hireDate: '2023-01-15',
          skills: ['Access Control', 'CCTV Monitoring', 'Patrol'],
          certifications: ['Security License', 'First Aid'],
          currentSite: 'ABC Bank Branch',
          emergencyContact: {
            name: 'Jane Smith',
            phone: '+961 70 654 321',
            relationship: 'Spouse',
          },
          performance: {
            rating: 4.5,
            completedShifts: 156,
            incidentReports: 3,
          },
        },
        {
          id: '2',
          employeeId: 'EMP002',
          firstName: 'Sarah',
          lastName: 'Johnson',
          email: '<EMAIL>',
          phone: '+961 71 234 567',
          status: 'active',
          role: 'supervisor',
          hireDate: '2022-08-20',
          skills: ['Team Leadership', 'Incident Response', 'Training'],
          certifications: ['Supervisor License', 'Crisis Management'],
          currentSite: 'XYZ Shopping Mall',
          emergencyContact: {
            name: 'Mike Johnson',
            phone: '+961 71 765 432',
            relationship: 'Brother',
          },
          performance: {
            rating: 4.8,
            completedShifts: 298,
            incidentReports: 1,
          },
        },
        {
          id: '3',
          employeeId: 'EMP003',
          firstName: 'Mike',
          lastName: 'Wilson',
          email: '<EMAIL>',
          phone: '+961 76 345 678',
          status: 'inactive',
          role: 'patrol_officer',
          hireDate: '2023-03-10',
          skills: ['Mobile Patrol', 'Report Writing', 'Emergency Response'],
          certifications: ['Security License', 'Driving License'],
          emergencyContact: {
            name: 'Lisa Wilson',
            phone: '+961 76 876 543',
            relationship: 'Wife',
          },
          performance: {
            rating: 4.2,
            completedShifts: 89,
            incidentReports: 2,
          },
        },
      ];

      dispatch(setAgents(mockAgents));
    } catch (err) {
      dispatch(setError('Failed to load agents'));
    } finally {
      dispatch(setLoading(false));
    }
  };

  const handleOpenDialog = (mode: 'add' | 'edit' | 'view', agent?: Agent) => {
    setDialogMode(mode);
    setSelectedAgent(agent || null);
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedAgent(null);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'success';
      case 'inactive': return 'default';
      case 'suspended': return 'error';
      default: return 'default';
    }
  };

  const getRoleLabel = (role: string) => {
    switch (role) {
      case 'security_guard': return 'Security Guard';
      case 'supervisor': return 'Supervisor';
      case 'patrol_officer': return 'Patrol Officer';
      default: return role;
    }
  };

  if (loading && agents.length === 0) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress size={60} />
      </Box>
    );
  }

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
          Agent Management
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => handleOpenDialog('add')}
        >
          Add New Agent
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <PersonIcon sx={{ fontSize: 40, color: 'primary.main', mr: 2 }} />
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                    {agents.filter(a => a.status === 'active').length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Active Agents
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <BadgeIcon sx={{ fontSize: 40, color: 'success.main', mr: 2 }} />
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                    {agents.filter(a => a.role === 'supervisor').length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Supervisors
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <PersonIcon sx={{ fontSize: 40, color: 'warning.main', mr: 2 }} />
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                    {agents.filter(a => a.currentSite).length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    On Assignment
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <PersonIcon sx={{ fontSize: 40, color: 'info.main', mr: 2 }} />
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                    {agents.length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total Agents
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Agents Table */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            All Agents
          </Typography>
          <TableContainer component={Paper} elevation={0}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Agent</TableCell>
                  <TableCell>Employee ID</TableCell>
                  <TableCell>Role</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Current Site</TableCell>
                  <TableCell>Performance</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {agents.map((agent) => (
                  <TableRow key={agent.id}>
                    <TableCell>
                      <Box display="flex" alignItems="center">
                        <Avatar sx={{ mr: 2 }}>
                          {agent.firstName[0]}{agent.lastName[0]}
                        </Avatar>
                        <Box>
                          <Typography variant="subtitle2">
                            {agent.firstName} {agent.lastName}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {agent.email}
                          </Typography>
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell>{agent.employeeId}</TableCell>
                    <TableCell>{getRoleLabel(agent.role)}</TableCell>
                    <TableCell>
                      <Chip
                        label={agent.status}
                        color={getStatusColor(agent.status) as any}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>{agent.currentSite || 'Unassigned'}</TableCell>
                    <TableCell>
                      <Box>
                        <Typography variant="body2">
                          Rating: {agent.performance.rating}/5
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {agent.performance.completedShifts} shifts
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <IconButton
                        size="small"
                        onClick={() => handleOpenDialog('view', agent)}
                      >
                        <ViewIcon />
                      </IconButton>
                      <IconButton
                        size="small"
                        onClick={() => handleOpenDialog('edit', agent)}
                      >
                        <EditIcon />
                      </IconButton>
                      <IconButton size="small" color="error">
                        <DeleteIcon />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* Agent Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {dialogMode === 'add' && 'Add New Agent'}
          {dialogMode === 'edit' && 'Edit Agent'}
          {dialogMode === 'view' && 'Agent Details'}
        </DialogTitle>
        <DialogContent>
          {selectedAgent && dialogMode === 'view' && (
            <Box>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Typography variant="h6" gutterBottom>
                    Personal Information
                  </Typography>
                  <Box mb={2}>
                    <Typography variant="subtitle2">Name</Typography>
                    <Typography>{selectedAgent.firstName} {selectedAgent.lastName}</Typography>
                  </Box>
                  <Box mb={2}>
                    <Typography variant="subtitle2">Email</Typography>
                    <Typography>{selectedAgent.email}</Typography>
                  </Box>
                  <Box mb={2}>
                    <Typography variant="subtitle2">Phone</Typography>
                    <Typography>{selectedAgent.phone}</Typography>
                  </Box>
                  <Box mb={2}>
                    <Typography variant="subtitle2">Hire Date</Typography>
                    <Typography>{new Date(selectedAgent.hireDate).toLocaleDateString()}</Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="h6" gutterBottom>
                    Professional Information
                  </Typography>
                  <Box mb={2}>
                    <Typography variant="subtitle2">Skills</Typography>
                    <Box>
                      {selectedAgent.skills.map((skill, index) => (
                        <Chip key={index} label={skill} size="small" sx={{ mr: 1, mb: 1 }} />
                      ))}
                    </Box>
                  </Box>
                  <Box mb={2}>
                    <Typography variant="subtitle2">Certifications</Typography>
                    <Box>
                      {selectedAgent.certifications.map((cert, index) => (
                        <Chip key={index} label={cert} size="small" sx={{ mr: 1, mb: 1 }} />
                      ))}
                    </Box>
                  </Box>
                </Grid>
              </Grid>
            </Box>
          )}
          {(dialogMode === 'add' || dialogMode === 'edit') && (
            <Box>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                Agent form fields would be implemented here
              </Typography>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>
            {dialogMode === 'view' ? 'Close' : 'Cancel'}
          </Button>
          {dialogMode !== 'view' && (
            <Button variant="contained">
              {dialogMode === 'add' ? 'Add Agent' : 'Save Changes'}
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default AgentManagementPage;
