{"version": 3, "file": "auth.d.ts", "sourceRoot": "", "sources": ["../../src/middleware/auth.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE,MAAM,SAAS,CAAC;AAI1D,OAAO,CAAC,MAAM,CAAC;IACb,UAAU,OAAO,CAAC;QAChB,UAAU,OAAO;YACf,IAAI,CAAC,EAAE;gBACL,MAAM,EAAE,MAAM,CAAC;gBACf,SAAS,EAAE,MAAM,CAAC;gBAClB,MAAM,EAAE,GAAG,CAAC;aACb,CAAC;YACF,IAAI,CAAC,EAAE;gBACL,EAAE,EAAE,MAAM,CAAC;gBACX,IAAI,EAAE,MAAM,CAAC;gBACb,KAAK,EAAE,MAAM,CAAC;aACf,CAAC;SACH;KACF;CACF;AAGD,eAAO,MAAM,WAAW,KAQtB,CAAC;AAGH,eAAO,MAAM,WAAW,GAAI,cAAc,MAAM,EAAE,MACxC,KAAK,OAAO,EAAE,KAAK,QAAQ,EAAE,MAAM,YAAY,mDAkCxD,CAAC;AAGF,eAAO,MAAM,YAAY,QArCV,OAAO,OAAO,QAAQ,QAAQ,YAAY,mDAqCO,CAAC;AAGjE,eAAO,MAAM,aAAa,QAxCX,OAAO,OAAO,QAAQ,QAAQ,YAAY,mDAwCL,CAAC;AAGrD,eAAO,MAAM,YAAY,QA3CV,OAAO,OAAO,QAAQ,QAAQ,YAAY,mDA2CgB,CAAC;AAG1E,eAAO,MAAM,YAAY,GAAI,KAAK,OAAO,EAAE,KAAK,QAAQ,EAAE,MAAM,YAAY,SAsB3E,CAAC;AAGF,eAAO,MAAM,eAAe,GAAI,OAAO,GAAG,EAAE,KAAK,OAAO,EAAE,KAAK,QAAQ,EAAE,MAAM,YAAY,mDAY1F,CAAC"}