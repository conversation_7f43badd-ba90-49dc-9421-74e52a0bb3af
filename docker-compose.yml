version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: agent-system-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: agent_system
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${DB_PASSWORD:-postgres}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    ports:
      - "5432:5432"
    networks:
      - agent-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d agent_system"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: agent-system-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-}
    volumes:
      - redis_data:/data
      - ./config/redis.conf:/usr/local/etc/redis/redis.conf:ro
    ports:
      - "6379:6379"
    networks:
      - agent-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  # Agent System Application
  app:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: agent-system-app
    restart: unless-stopped
    environment:
      NODE_ENV: production
      PORT: 3003
      DATABASE_URL: postgresql://postgres:${DB_PASSWORD:-postgres}@postgres:5432/agent_system
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: ${REDIS_PASSWORD:-}
      JWT_SECRET: ${JWT_SECRET}
      ENCRYPTION_KEY: ${ENCRYPTION_KEY}
      WEATHER_API_KEY: ${WEATHER_API_KEY}
      GOOGLE_MAPS_API_KEY: ${GOOGLE_MAPS_API_KEY}
      SECURITY_API_KEY: ${SECURITY_API_KEY}
      MARKET_DATA_API_KEY: ${MARKET_DATA_API_KEY}
      SENDGRID_API_KEY: ${SENDGRID_API_KEY}
      TWILIO_ACCOUNT_SID: ${TWILIO_ACCOUNT_SID}
      TWILIO_AUTH_TOKEN: ${TWILIO_AUTH_TOKEN}
      TWILIO_PHONE_NUMBER: ${TWILIO_PHONE_NUMBER}
      SLACK_WEBHOOK_URL: ${SLACK_WEBHOOK_URL}
      ALERT_EMAIL_TO: ${ALERT_EMAIL_TO}
      ALERTING_ENABLED: ${ALERTING_ENABLED:-false}
      LOG_LEVEL: ${LOG_LEVEL:-info}
    env_file:
      - .env
    ports:
      - "3003:3003"
    volumes:
      - app_logs:/app/logs
      - ./uploads:/app/uploads
    networks:
      - agent-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3003/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Admin Portal
  admin-portal:
    build:
      context: ./admin-portal
      dockerfile: Dockerfile
      target: production
    container_name: bahinlink-admin-portal
    environment:
      REACT_APP_API_URL: ${REACT_APP_API_URL:-http://localhost:3000/api}
      REACT_APP_CLERK_PUBLISHABLE_KEY: ${CLERK_PUBLISHABLE_KEY}
      REACT_APP_GOOGLE_MAPS_API_KEY: ${GOOGLE_MAPS_API_KEY}
    ports:
      - "3001:80"
    depends_on:
      - backend
    networks:
      - bahinlink-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Client Portal
  client-portal:
    build:
      context: ./client-portal
      dockerfile: Dockerfile
      target: production
    container_name: bahinlink-client-portal
    environment:
      REACT_APP_API_URL: ${REACT_APP_API_URL:-http://localhost:3000/api}
      REACT_APP_CLERK_PUBLISHABLE_KEY: ${CLERK_PUBLISHABLE_KEY}
    ports:
      - "3002:80"
    depends_on:
      - backend
    networks:
      - bahinlink-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: bahinlink-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./ssl:/etc/nginx/ssl
      - ./nginx/logs:/var/log/nginx
    depends_on:
      - backend
      - admin-portal
      - client-portal
    networks:
      - bahinlink-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Monitoring - Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: bahinlink-prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - bahinlink-network
    restart: unless-stopped

  # Monitoring - Grafana
  grafana:
    image: grafana/grafana:latest
    container_name: bahinlink-grafana
    environment:
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD:-admin}
      GF_USERS_ALLOW_SIGN_UP: false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    ports:
      - "3003:3000"
    depends_on:
      - prometheus
    networks:
      - bahinlink-network
    restart: unless-stopped

  # Log Management - Elasticsearch
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    container_name: bahinlink-elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    networks:
      - bahinlink-network
    restart: unless-stopped

  # Log Management - Kibana
  kibana:
    image: docker.elastic.co/kibana/kibana:8.8.0
    container_name: bahinlink-kibana
    environment:
      ELASTICSEARCH_HOSTS: http://elasticsearch:9200
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch
    networks:
      - bahinlink-network
    restart: unless-stopped

  # Backup Service
  backup:
    build:
      context: ./scripts/backup
      dockerfile: Dockerfile
    container_name: bahinlink-backup
    environment:
      POSTGRES_HOST: postgres
      POSTGRES_DB: bahinlink
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-postgres}
      AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID}
      AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY}
      AWS_S3_BACKUP_BUCKET: ${AWS_S3_BACKUP_BUCKET}
      BACKUP_SCHEDULE: ${BACKUP_SCHEDULE:-0 2 * * *}
    volumes:
      - ./backups:/backups
    depends_on:
      - postgres
    networks:
      - bahinlink-network
    restart: unless-stopped

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  elasticsearch_data:
    driver: local

networks:
  bahinlink-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
