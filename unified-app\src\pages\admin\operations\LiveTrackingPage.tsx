import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Switch,
  FormControlLabel,
  Alert,
  CircularProgress,
} from '@mui/material';
import {
  LocationOn as LocationIcon,
  Person as PersonIcon,
  Security as SecurityIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import { useAppSelector, useAppDispatch } from '../../../store/hooks';
import { setAgents, setLoading, setError } from '../../../store/slices/admin/agentsSlice';

interface Agent {
  id: string;
  name: string;
  status: 'on-duty' | 'off-duty' | 'break' | 'emergency';
  location: {
    lat: number;
    lng: number;
    address: string;
    lastUpdate: string;
  };
  site: {
    id: string;
    name: string;
  };
  shift: {
    start: string;
    end: string;
  };
  battery: number;
  isOnline: boolean;
}

const LiveTrackingPage: React.FC = () => {
  const dispatch = useAppDispatch();
  const { agents, loading, error } = useAppSelector(state => state.admin.agents);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [selectedAgent, setSelectedAgent] = useState<string | null>(null);

  useEffect(() => {
    loadAgentData();
    
    if (autoRefresh) {
      const interval = setInterval(loadAgentData, 30000); // Refresh every 30 seconds
      return () => clearInterval(interval);
    }
  }, [autoRefresh]);

  const loadAgentData = async () => {
    dispatch(setLoading(true));
    try {
      // Mock data - in real app, this would be an API call
      const mockAgents: Agent[] = [
        {
          id: '1',
          name: 'John Smith',
          status: 'on-duty',
          location: {
            lat: 33.8938,
            lng: 35.5018,
            address: 'Downtown Beirut, Lebanon',
            lastUpdate: new Date().toISOString(),
          },
          site: { id: 'site1', name: 'ABC Bank Branch' },
          shift: { start: '08:00', end: '16:00' },
          battery: 85,
          isOnline: true,
        },
        {
          id: '2',
          name: 'Sarah Johnson',
          status: 'on-duty',
          location: {
            lat: 33.8869,
            lng: 35.5131,
            address: 'Hamra District, Beirut',
            lastUpdate: new Date(Date.now() - 5 * 60000).toISOString(),
          },
          site: { id: 'site2', name: 'XYZ Shopping Mall' },
          shift: { start: '16:00', end: '00:00' },
          battery: 92,
          isOnline: true,
        },
        {
          id: '3',
          name: 'Mike Wilson',
          status: 'break',
          location: {
            lat: 33.8547,
            lng: 35.4856,
            address: 'Verdun Area, Beirut',
            lastUpdate: new Date(Date.now() - 15 * 60000).toISOString(),
          },
          site: { id: 'site3', name: 'Corporate Office Building' },
          shift: { start: '00:00', end: '08:00' },
          battery: 45,
          isOnline: true,
        },
      ];

      dispatch(setAgents(mockAgents));
    } catch (err) {
      dispatch(setError('Failed to load agent tracking data'));
    } finally {
      dispatch(setLoading(false));
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'on-duty': return 'success';
      case 'off-duty': return 'default';
      case 'break': return 'warning';
      case 'emergency': return 'error';
      default: return 'default';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'on-duty': return <CheckCircleIcon />;
      case 'off-duty': return <PersonIcon />;
      case 'break': return <WarningIcon />;
      case 'emergency': return <WarningIcon />;
      default: return <PersonIcon />;
    }
  };

  if (loading && agents.length === 0) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress size={60} />
      </Box>
    );
  }

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
          Live Agent Tracking
        </Typography>
        <Box display="flex" gap={2} alignItems="center">
          <FormControlLabel
            control={
              <Switch
                checked={autoRefresh}
                onChange={(e) => setAutoRefresh(e.target.checked)}
              />
            }
            label="Auto Refresh"
          />
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={loadAgentData}
            disabled={loading}
          >
            Refresh
          </Button>
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* Agent List */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Active Agents ({agents.length})
              </Typography>
              <List>
                {agents.map((agent) => (
                  <ListItem
                    key={agent.id}
                    button
                    selected={selectedAgent === agent.id}
                    onClick={() => setSelectedAgent(agent.id)}
                    sx={{
                      border: selectedAgent === agent.id ? 2 : 1,
                      borderColor: selectedAgent === agent.id ? 'primary.main' : 'divider',
                      borderRadius: 1,
                      mb: 1,
                    }}
                  >
                    <ListItemIcon>
                      {getStatusIcon(agent.status)}
                    </ListItemIcon>
                    <ListItemText
                      primary={
                        <Box display="flex" justifyContent="space-between" alignItems="center">
                          <Typography variant="subtitle1">{agent.name}</Typography>
                          <Chip
                            label={agent.status}
                            color={getStatusColor(agent.status) as any}
                            size="small"
                          />
                        </Box>
                      }
                      secondary={
                        <Box>
                          <Typography variant="body2" color="text.secondary">
                            {agent.site.name}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            Battery: {agent.battery}% | {agent.isOnline ? 'Online' : 'Offline'}
                          </Typography>
                        </Box>
                      }
                    />
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Map and Details */}
        <Grid item xs={12} md={8}>
          <Card sx={{ height: '600px' }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Real-time Location Map
              </Typography>
              <Box
                sx={{
                  height: '500px',
                  backgroundColor: 'grey.100',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  borderRadius: 1,
                }}
              >
                <Box textAlign="center">
                  <LocationIcon sx={{ fontSize: 64, color: 'grey.400', mb: 2 }} />
                  <Typography variant="h6" color="text.secondary">
                    Interactive Map
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Real-time agent locations will be displayed here
                  </Typography>
                  <Typography variant="caption" color="text.secondary" display="block" mt={1}>
                    Integration with Google Maps or Leaflet required
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Agent Details */}
        {selectedAgent && (
          <Grid item xs={12}>
            {(() => {
              const agent = agents.find(a => a.id === selectedAgent);
              if (!agent) return null;

              return (
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Agent Details: {agent.name}
                    </Typography>
                    <Grid container spacing={3}>
                      <Grid item xs={12} md={4}>
                        <Box>
                          <Typography variant="subtitle2" color="text.secondary">
                            Current Status
                          </Typography>
                          <Chip
                            label={agent.status}
                            color={getStatusColor(agent.status) as any}
                            sx={{ mt: 1 }}
                          />
                        </Box>
                      </Grid>
                      <Grid item xs={12} md={4}>
                        <Box>
                          <Typography variant="subtitle2" color="text.secondary">
                            Current Location
                          </Typography>
                          <Typography variant="body1">
                            {agent.location.address}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            Last updated: {new Date(agent.location.lastUpdate).toLocaleTimeString()}
                          </Typography>
                        </Box>
                      </Grid>
                      <Grid item xs={12} md={4}>
                        <Box>
                          <Typography variant="subtitle2" color="text.secondary">
                            Shift Information
                          </Typography>
                          <Typography variant="body1">
                            {agent.shift.start} - {agent.shift.end}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            Site: {agent.site.name}
                          </Typography>
                        </Box>
                      </Grid>
                    </Grid>
                  </CardContent>
                </Card>
              );
            })()}
          </Grid>
        )}
      </Grid>
    </Box>
  );
};

export default LiveTrackingPage;
