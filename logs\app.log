{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  seedType: 'basic',
  force: false,
  skipExisting: true,
  level: 'info',
  message: 'Starting database seeding process',
  timestamp: '2025-06-19 11:51:52'
}
{
  message: 'Seeding system configuration',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-06-19 11:52:00'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  created: [
    'SYSTEM_INITIALIZED',
    'DEFAULT_SETTINGS',
    'NOTIFICATION_SETTINGS',
    'SECURITY_SETTINGS'
  ],
  level: 'info',
  message: 'System configuration seeded',
  timestamp: '2025-06-19 11:52:01'
}
{
  message: 'Seeding admin user',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-06-19 11:52:01'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  seedType: 'basic',
  force: false,
  skipExisting: true,
  level: 'info',
  message: 'Starting database seeding process',
  timestamp: '2025-06-19 11:54:14'
}
{
  message: 'Seeding system configuration',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-06-19 11:54:16'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  created: [
    'SYSTEM_INITIALIZED',
    'DEFAULT_SETTINGS',
    'NOTIFICATION_SETTINGS',
    'SECURITY_SETTINGS'
  ],
  level: 'info',
  message: 'System configuration seeded',
  timestamp: '2025-06-19 11:54:19'
}
{
  message: 'Seeding admin user',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-06-19 11:54:19'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  id: '71d1b59c-59be-4543-bbde-46ad53fd1528',
  username: 'admin',
  email: '<EMAIL>',
  password: '!gcdY@nP*C5iV&*q',
  level: 'info',
  message: 'Admin user created',
  timestamp: '2025-06-19 11:54:20'
}
{
  message: 'Seeding basic data',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-06-19 11:54:20'
}
{
  error: Error: ENOENT: no such file or directory, open 'C:\Users\<USER>\Pictures\agent\finalagent-main\test\data\05-versions-space.pdf'
      at Object.openSync (node:fs:574:18)
      at Object.readFileSync (node:fs:453:35)
      at Object.<anonymous> (C:\Users\<USER>\Pictures\agent\finalagent-main\node_modules\pdf-parse\index.js:15:25)
      at Module._compile (node:internal/modules/cjs/loader:1529:14)
      at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)
      at Module.load (node:internal/modules/cjs/loader:1275:32)
      at Module._load (node:internal/modules/cjs/loader:1096:12)
      at cjsLoader (node:internal/modules/esm/translators:298:15)
      at ModuleWrap.<anonymous> (node:internal/modules/esm/translators:240:7)
      at ModuleJob.run (node:internal/modules/esm/module_job:263:25) {
    errno: -4058,
    code: 'ENOENT',
    syscall: 'open',
    path: 'C:\\Users\\<USER>\\Pictures\\agent\\finalagent-main\\test\\data\\05-versions-space.pdf'
  },
  level: 'error',
  message: "unhandledRejection: ENOENT: no such file or directory, open 'C:\\Users\\<USER>\\Pictures\\agent\\finalagent-main\\test\\data\\05-versions-space.pdf'\n" +
    "Error: ENOENT: no such file or directory, open 'C:\\Users\\<USER>\\Pictures\\agent\\finalagent-main\\test\\data\\05-versions-space.pdf'\n" +
    '    at Object.openSync (node:fs:574:18)\n' +
    '    at Object.readFileSync (node:fs:453:35)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Pictures\\agent\\finalagent-main\\node_modules\\pdf-parse\\index.js:15:25)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n' +
    '    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1275:32)\n' +
    '    at Module._load (node:internal/modules/cjs/loader:1096:12)\n' +
    '    at cjsLoader (node:internal/modules/esm/translators:298:15)\n' +
    '    at ModuleWrap.<anonymous> (node:internal/modules/esm/translators:240:7)\n' +
    '    at ModuleJob.run (node:internal/modules/esm/module_job:263:25)',
  stack: "Error: ENOENT: no such file or directory, open 'C:\\Users\\<USER>\\Pictures\\agent\\finalagent-main\\test\\data\\05-versions-space.pdf'\n" +
    '    at Object.openSync (node:fs:574:18)\n' +
    '    at Object.readFileSync (node:fs:453:35)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Pictures\\agent\\finalagent-main\\node_modules\\pdf-parse\\index.js:15:25)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n' +
    '    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1275:32)\n' +
    '    at Module._load (node:internal/modules/cjs/loader:1096:12)\n' +
    '    at cjsLoader (node:internal/modules/esm/translators:298:15)\n' +
    '    at ModuleWrap.<anonymous> (node:internal/modules/esm/translators:240:7)\n' +
    '    at ModuleJob.run (node:internal/modules/esm/module_job:263:25)',
  rejection: true,
  date: 'Mon Jul 07 2025 17:51:35 GMT+0100 (West Africa Standard Time)',
  process: {
    pid: 6700,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Pictures\\agent\\finalagent-main',
    execPath: 'C:\\nvm4w\\nodejs\\node.exe',
    version: 'v20.19.2',
    argv: [
      'C:\\nvm4w\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Pictures\\agent\\finalagent-main\\src\\server.js'
    ],
    memoryUsage: {
      rss: 85028864,
      heapTotal: 46055424,
      heapUsed: 33770592,
      external: 3524547,
      arrayBuffers: 66714
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 95020.312 },
  trace: [
    {
      column: 18,
      file: 'node:fs',
      function: 'Object.openSync',
      line: 574,
      method: 'openSync',
      native: false
    },
    {
      column: 35,
      file: 'node:fs',
      function: 'Object.readFileSync',
      line: 453,
      method: 'readFileSync',
      native: false
    },
    {
      column: 25,
      file: 'C:\\Users\\<USER>\\Pictures\\agent\\finalagent-main\\node_modules\\pdf-parse\\index.js',
      function: null,
      line: 15,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1529,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._extensions..js',
      line: 1613,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1275,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._load',
      line: 1096,
      method: '_load',
      native: false
    },
    {
      column: 15,
      file: 'node:internal/modules/esm/translators',
      function: 'cjsLoader',
      line: 298,
      method: null,
      native: false
    },
    {
      column: 7,
      file: 'node:internal/modules/esm/translators',
      function: null,
      line: 240,
      method: null,
      native: false
    },
    {
      column: 25,
      file: 'node:internal/modules/esm/module_job',
      function: 'ModuleJob.run',
      line: 263,
      method: 'run',
      native: false
    }
  ],
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-07 17:51:35'
}
{
  error: Error: ENOENT: no such file or directory, open 'C:\Users\<USER>\Pictures\agent\finalagent-main\test\data\05-versions-space.pdf'
      at Object.openSync (node:fs:574:18)
      at Object.readFileSync (node:fs:453:35)
      at Object.<anonymous> (C:\Users\<USER>\Pictures\agent\finalagent-main\node_modules\pdf-parse\index.js:15:25)
      at Module._compile (node:internal/modules/cjs/loader:1529:14)
      at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)
      at Module.load (node:internal/modules/cjs/loader:1275:32)
      at Module._load (node:internal/modules/cjs/loader:1096:12)
      at cjsLoader (node:internal/modules/esm/translators:298:15)
      at ModuleWrap.<anonymous> (node:internal/modules/esm/translators:240:7)
      at ModuleJob.run (node:internal/modules/esm/module_job:263:25) {
    errno: -4058,
    code: 'ENOENT',
    syscall: 'open',
    path: 'C:\\Users\\<USER>\\Pictures\\agent\\finalagent-main\\test\\data\\05-versions-space.pdf'
  },
  level: 'error',
  message: "unhandledRejection: ENOENT: no such file or directory, open 'C:\\Users\\<USER>\\Pictures\\agent\\finalagent-main\\test\\data\\05-versions-space.pdf'\n" +
    "Error: ENOENT: no such file or directory, open 'C:\\Users\\<USER>\\Pictures\\agent\\finalagent-main\\test\\data\\05-versions-space.pdf'\n" +
    '    at Object.openSync (node:fs:574:18)\n' +
    '    at Object.readFileSync (node:fs:453:35)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Pictures\\agent\\finalagent-main\\node_modules\\pdf-parse\\index.js:15:25)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n' +
    '    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1275:32)\n' +
    '    at Module._load (node:internal/modules/cjs/loader:1096:12)\n' +
    '    at cjsLoader (node:internal/modules/esm/translators:298:15)\n' +
    '    at ModuleWrap.<anonymous> (node:internal/modules/esm/translators:240:7)\n' +
    '    at ModuleJob.run (node:internal/modules/esm/module_job:263:25)',
  stack: "Error: ENOENT: no such file or directory, open 'C:\\Users\\<USER>\\Pictures\\agent\\finalagent-main\\test\\data\\05-versions-space.pdf'\n" +
    '    at Object.openSync (node:fs:574:18)\n' +
    '    at Object.readFileSync (node:fs:453:35)\n' +
    '    at Object.<anonymous> (C:\\Users\\<USER>\\Pictures\\agent\\finalagent-main\\node_modules\\pdf-parse\\index.js:15:25)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1529:14)\n' +
    '    at Module._extensions..js (node:internal/modules/cjs/loader:1613:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1275:32)\n' +
    '    at Module._load (node:internal/modules/cjs/loader:1096:12)\n' +
    '    at cjsLoader (node:internal/modules/esm/translators:298:15)\n' +
    '    at ModuleWrap.<anonymous> (node:internal/modules/esm/translators:240:7)\n' +
    '    at ModuleJob.run (node:internal/modules/esm/module_job:263:25)',
  rejection: true,
  date: 'Mon Jul 07 2025 17:56:48 GMT+0100 (West Africa Standard Time)',
  process: {
    pid: 13964,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Pictures\\agent\\finalagent-main',
    execPath: 'C:\\nvm4w\\nodejs\\node.exe',
    version: 'v20.19.2',
    argv: [
      'C:\\nvm4w\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Pictures\\agent\\finalagent-main\\src\\server.js'
    ],
    memoryUsage: {
      rss: 106569728,
      heapTotal: 67448832,
      heapUsed: 47845216,
      external: 3578033,
      arrayBuffers: 118678
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 95333.562 },
  trace: [
    {
      column: 18,
      file: 'node:fs',
      function: 'Object.openSync',
      line: 574,
      method: 'openSync',
      native: false
    },
    {
      column: 35,
      file: 'node:fs',
      function: 'Object.readFileSync',
      line: 453,
      method: 'readFileSync',
      native: false
    },
    {
      column: 25,
      file: 'C:\\Users\\<USER>\\Pictures\\agent\\finalagent-main\\node_modules\\pdf-parse\\index.js',
      function: null,
      line: 15,
      method: null,
      native: false
    },
    {
      column: 14,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._compile',
      line: 1529,
      method: '_compile',
      native: false
    },
    {
      column: 10,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._extensions..js',
      line: 1613,
      method: '.js',
      native: false
    },
    {
      column: 32,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module.load',
      line: 1275,
      method: 'load',
      native: false
    },
    {
      column: 12,
      file: 'node:internal/modules/cjs/loader',
      function: 'Module._load',
      line: 1096,
      method: '_load',
      native: false
    },
    {
      column: 15,
      file: 'node:internal/modules/esm/translators',
      function: 'cjsLoader',
      line: 298,
      method: null,
      native: false
    },
    {
      column: 7,
      file: 'node:internal/modules/esm/translators',
      function: null,
      line: 240,
      method: null,
      native: false
    },
    {
      column: 25,
      file: 'node:internal/modules/esm/module_job',
      function: 'ModuleJob.run',
      line: 263,
      method: 'run',
      native: false
    }
  ],
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-07 17:56:48'
}
{
  message: 'WebSocket service initialized',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-07 18:08:31'
}
{
  message: 'WebSocket service initialized',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-07 18:10:35'
}
{
  message: 'WebSocket service initialized',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-07 18:12:55'
}
{
  message: 'Database connected successfully',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-07 18:12:55'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis Client Error (non-critical):',
  timestamp: '2025-07-07 18:12:55'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis Client Error (non-critical):',
  timestamp: '2025-07-07 18:12:56'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis Client Error (non-critical):',
  timestamp: '2025-07-07 18:12:56'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis Client Error (non-critical):',
  timestamp: '2025-07-07 18:12:57'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis Client Error (non-critical):',
  timestamp: '2025-07-07 18:12:57'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis Client Error (non-critical):',
  timestamp: '2025-07-07 18:12:58'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis Client Error (non-critical):',
  timestamp: '2025-07-07 18:13:00'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis initialization failed (continuing without Redis):',
  timestamp: '2025-07-07 18:13:00'
}
{
  message: 'WebSocket service initialized',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-07 18:31:07'
}
{
  message: 'Database connected successfully',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-07 18:31:07'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis Client Error (non-critical):',
  timestamp: '2025-07-07 18:31:07'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis Client Error (non-critical):',
  timestamp: '2025-07-07 18:31:08'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis Client Error (non-critical):',
  timestamp: '2025-07-07 18:31:08'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis Client Error (non-critical):',
  timestamp: '2025-07-07 18:31:09'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis Client Error (non-critical):',
  timestamp: '2025-07-07 18:31:09'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis Client Error (non-critical):',
  timestamp: '2025-07-07 18:31:10'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis Client Error (non-critical):',
  timestamp: '2025-07-07 18:31:12'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis initialization failed (continuing without Redis):',
  timestamp: '2025-07-07 18:31:12'
}
{
  message: 'WebSocket service initialized',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-07 18:39:19'
}
{
  message: 'Database connected successfully',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-07 18:39:19'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis Client Error (non-critical):',
  timestamp: '2025-07-07 18:39:19'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis Client Error (non-critical):',
  timestamp: '2025-07-07 18:39:20'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis Client Error (non-critical):',
  timestamp: '2025-07-07 18:39:20'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis Client Error (non-critical):',
  timestamp: '2025-07-07 18:39:21'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis Client Error (non-critical):',
  timestamp: '2025-07-07 18:39:21'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis Client Error (non-critical):',
  timestamp: '2025-07-07 18:39:22'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis Client Error (non-critical):',
  timestamp: '2025-07-07 18:39:24'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis initialization failed (continuing without Redis):',
  timestamp: '2025-07-07 18:39:24'
}
{
  message: 'WebSocket service initialized',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-07 18:40:46'
}
{
  message: 'Database connected successfully',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-07 18:40:46'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis Client Error (non-critical):',
  timestamp: '2025-07-07 18:40:46'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis Client Error (non-critical):',
  timestamp: '2025-07-07 18:40:47'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis Client Error (non-critical):',
  timestamp: '2025-07-07 18:40:47'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis Client Error (non-critical):',
  timestamp: '2025-07-07 18:40:48'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis Client Error (non-critical):',
  timestamp: '2025-07-07 18:40:48'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis Client Error (non-critical):',
  timestamp: '2025-07-07 18:40:49'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis Client Error (non-critical):',
  timestamp: '2025-07-07 18:40:51'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis initialization failed (continuing without Redis):',
  timestamp: '2025-07-07 18:40:51'
}
{
  message: 'WebSocket service initialized',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-07 18:44:03'
}
{
  message: 'Database connected successfully',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-07 18:44:03'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis Client Error (non-critical):',
  timestamp: '2025-07-07 18:44:03'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis Client Error (non-critical):',
  timestamp: '2025-07-07 18:44:04'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis Client Error (non-critical):',
  timestamp: '2025-07-07 18:44:04'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis Client Error (non-critical):',
  timestamp: '2025-07-07 18:44:04'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis Client Error (non-critical):',
  timestamp: '2025-07-07 18:44:05'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis Client Error (non-critical):',
  timestamp: '2025-07-07 18:44:06'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis Client Error (non-critical):',
  timestamp: '2025-07-07 18:44:08'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis initialization failed (continuing without Redis):',
  timestamp: '2025-07-07 18:44:08'
}
{
  message: 'WebSocket service initialized',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-07 18:47:28'
}
{
  message: 'Database connected successfully',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-07 18:47:28'
}
{
  message: 'Starting Redis initialization...',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-07 18:47:28'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis Client Error (non-critical):',
  timestamp: '2025-07-07 18:47:29'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis Client Error (non-critical):',
  timestamp: '2025-07-07 18:47:30'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis Client Error (non-critical):',
  timestamp: '2025-07-07 18:47:30'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis Client Error (non-critical):',
  timestamp: '2025-07-07 18:47:30'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis Client Error (non-critical):',
  timestamp: '2025-07-07 18:47:31'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis Client Error (non-critical):',
  timestamp: '2025-07-07 18:47:32'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis Client Error (non-critical):',
  timestamp: '2025-07-07 18:47:33'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis initialization failed (continuing without Redis):',
  timestamp: '2025-07-07 18:47:34'
}
{
  message: 'WebSocket service initialized',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-07 18:50:35'
}
{
  message: 'Database connected successfully',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-07 18:50:35'
}
{
  message: 'Starting Redis initialization...',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-07 18:50:35'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis Client Error (non-critical):',
  timestamp: '2025-07-07 18:50:35'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis Client Error (non-critical):',
  timestamp: '2025-07-07 18:50:36'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis Client Error (non-critical):',
  timestamp: '2025-07-07 18:50:36'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis Client Error (non-critical):',
  timestamp: '2025-07-07 18:50:36'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis Client Error (non-critical):',
  timestamp: '2025-07-07 18:50:37'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis Client Error (non-critical):',
  timestamp: '2025-07-07 18:50:38'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis Client Error (non-critical):',
  timestamp: '2025-07-07 18:50:40'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis initialization failed (continuing without Redis):',
  timestamp: '2025-07-07 18:50:40'
}
{
  message: 'WebSocket service initialized',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-07 19:01:54'
}
{
  message: 'Database connected successfully',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-07 19:01:55'
}
{
  message: 'Starting Redis initialization...',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-07 19:01:55'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis Client Error (non-critical):',
  timestamp: '2025-07-07 19:01:55'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis Client Error (non-critical):',
  timestamp: '2025-07-07 19:01:56'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis Client Error (non-critical):',
  timestamp: '2025-07-07 19:01:56'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis Client Error (non-critical):',
  timestamp: '2025-07-07 19:01:57'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis Client Error (non-critical):',
  timestamp: '2025-07-07 19:01:57'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis Client Error (non-critical):',
  timestamp: '2025-07-07 19:01:58'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis initialization failed (continuing without Redis):',
  timestamp: '2025-07-07 19:02:00'
}
{
  message: 'WebSocket service initialized',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-07 19:03:41'
}
{
  message: 'Database connected successfully',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-07 19:03:41'
}
{
  message: 'Starting Redis initialization...',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-07 19:03:41'
}
{
  message: 'Redis initialization started (non-blocking)',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-07 19:03:41'
}
{
  message: 'Starting WebSocket service initialization...',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-07 19:03:41'
}
{
  message: 'WebSocket service initialized',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-07 19:03:41'
}
{
  message: 'WebSocket service created successfully',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-07 19:03:41'
}
{
  message: 'Starting Notification service initialization...',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-07 19:03:41'
}
{
  message: 'Notification service created successfully',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-07 19:03:41'
}
{
  message: 'WebSocket and Notification services initialized successfully',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-07 19:03:41'
}
{
  message: 'Server running on port 3000 in development mode',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-07 19:03:41'
}
{
  message: 'API documentation available at http://localhost:3000/api-docs',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-07 19:03:41'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis Client Error (non-critical):',
  timestamp: '2025-07-07 19:03:42'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis Client Error (non-critical):',
  timestamp: '2025-07-07 19:03:43'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis Client Error (non-critical):',
  timestamp: '2025-07-07 19:03:43'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis Client Error (non-critical):',
  timestamp: '2025-07-07 19:03:43'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis Client Error (non-critical):',
  timestamp: '2025-07-07 19:03:44'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis Client Error (non-critical):',
  timestamp: '2025-07-07 19:03:45'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis initialization failed (continuing without Redis):',
  timestamp: '2025-07-07 19:03:46'
}
{
  message: 'Received SIGINT. Starting graceful shutdown...',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-08 10:09:05'
}
{
  message: 'WebSocket service initialized',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-08 10:10:18'
}
{
  message: 'Database connected successfully',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-08 10:10:19'
}
{
  message: 'Starting Redis initialization...',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-08 10:10:19'
}
{
  message: 'Redis initialization started (non-blocking)',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-08 10:10:19'
}
{
  message: 'Starting WebSocket service initialization...',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-08 10:10:19'
}
{
  message: 'WebSocket service initialized',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-08 10:10:19'
}
{
  message: 'WebSocket service created successfully',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-08 10:10:19'
}
{
  message: 'Starting Notification service initialization...',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-08 10:10:19'
}
{
  message: 'Notification service created successfully',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-08 10:10:19'
}
{
  message: 'WebSocket and Notification services initialized successfully',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-08 10:10:19'
}
{
  message: 'Server running on port 3000 in development mode',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-08 10:10:19'
}
{
  message: 'API documentation available at http://localhost:3000/api-docs',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-08 10:10:19'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis Client Error (non-critical):',
  timestamp: '2025-07-08 10:10:19'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis Client Error (non-critical):',
  timestamp: '2025-07-08 10:10:22'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis Client Error (non-critical):',
  timestamp: '2025-07-08 10:10:23'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis Client Error (non-critical):',
  timestamp: '2025-07-08 10:10:23'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis Client Error (non-critical):',
  timestamp: '2025-07-08 10:10:23'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis initialization failed (continuing without Redis):',
  timestamp: '2025-07-08 10:10:24'
}
{
  message: 'WebSocket service initialized',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-09 14:28:35'
}
{
  message: 'Database connected successfully',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-09 14:28:36'
}
{
  message: 'Starting Redis initialization...',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-09 14:28:36'
}
{
  message: 'Redis initialization started (non-blocking)',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-09 14:28:36'
}
{
  message: 'Starting WebSocket service initialization...',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-09 14:28:36'
}
{
  message: 'WebSocket service initialized',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-09 14:28:36'
}
{
  message: 'WebSocket service created successfully',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-09 14:28:36'
}
{
  message: 'Starting Notification service initialization...',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-09 14:28:36'
}
{
  message: 'Notification service created successfully',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-09 14:28:36'
}
{
  message: 'WebSocket and Notification services initialized successfully',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-09 14:28:36'
}
{
  message: 'Server running on port 3000 in development mode',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-09 14:28:36'
}
{
  message: 'API documentation available at http://localhost:3000/api-docs',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-09 14:28:36'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis Client Error (non-critical):',
  timestamp: '2025-07-09 14:28:37'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis initialization failed (continuing without Redis):',
  timestamp: '2025-07-09 14:28:43'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis Client Error (non-critical):',
  timestamp: '2025-07-09 14:28:43'
}
{
  message: 'Redis initialization completed successfully',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-09 14:28:43'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  requestId: '65a90648-2bb4-4f43-90f6-94b9ace8457b',
  method: 'GET',
  url: '/',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  contentType: undefined,
  contentLength: undefined,
  userId: undefined,
  type: 'request_start',
  level: 'info',
  message: 'Incoming Request',
  timestamp: '2025-07-09 15:07:53'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  requestId: '65a90648-2bb4-4f43-90f6-94b9ace8457b',
  method: 'GET',
  url: '/',
  statusCode: 200,
  responseTime: '8ms',
  contentLength: 138,
  userId: undefined,
  type: 'request_end',
  level: 'info',
  message: 'Outgoing Response',
  timestamp: '2025-07-09 15:07:53'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  requestId: '65a90648-2bb4-4f43-90f6-94b9ace8457b',
  method: 'GET',
  url: '/',
  statusCode: 200,
  responseTime: '11ms',
  contentLength: 138,
  userId: undefined,
  type: 'request_end',
  level: 'info',
  message: 'Outgoing Response',
  timestamp: '2025-07-09 15:07:53'
}
{
  message: '::1 - - [09/Jul/2025:14:07:53 +0000] "GET / HTTP/1.1" 200 138 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-09 15:07:53'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  requestId: 'd1367bfc-4b87-4c7c-8760-b3199e640305',
  method: 'GET',
  url: '/favicon.ico',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  contentType: undefined,
  contentLength: undefined,
  userId: undefined,
  type: 'request_start',
  level: 'info',
  message: 'Incoming Request',
  timestamp: '2025-07-09 15:07:53'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  statusCode: 404,
  message: 'API Error: Route /favicon.ico not found',
  stack: 'Error: Route /favicon.ico not found\n' +
    '    at notFoundHandler (file:///C:/Users/<USER>/Pictures/agent/finalagent-main/src/middleware/errorHandler.js:165:17)\n' +
    '    at newFn (C:\\Users\\<USER>\\Pictures\\agent\\finalagent-main\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Pictures\\agent\\finalagent-main\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (C:\\Users\\<USER>\\Pictures\\agent\\finalagent-main\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at C:\\Users\\<USER>\\Pictures\\agent\\finalagent-main\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (C:\\Users\\<USER>\\Pictures\\agent\\finalagent-main\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (C:\\Users\\<USER>\\Pictures\\agent\\finalagent-main\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at requestLogger (file:///C:/Users/<USER>/Pictures/agent/finalagent-main/src/middleware/requestLogger.js:102:3)\n' +
    '    at newFn (C:\\Users\\<USER>\\Pictures\\agent\\finalagent-main\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Pictures\\agent\\finalagent-main\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  url: '/favicon.ico',
  method: 'GET',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  requestId: 'd1367bfc-4b87-4c7c-8760-b3199e640305',
  level: 'warn',
  timestamp: '2025-07-09 15:07:54'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  requestId: 'd1367bfc-4b87-4c7c-8760-b3199e640305',
  method: 'GET',
  url: '/favicon.ico',
  statusCode: 404,
  responseTime: '12ms',
  contentLength: 1424,
  userId: undefined,
  type: 'request_end',
  level: 'info',
  message: 'Outgoing Response',
  timestamp: '2025-07-09 15:07:54'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  requestId: 'd1367bfc-4b87-4c7c-8760-b3199e640305',
  method: 'GET',
  url: '/favicon.ico',
  statusCode: 404,
  responseTime: '19ms',
  contentLength: 1424,
  userId: undefined,
  type: 'request_end',
  level: 'info',
  message: 'Outgoing Response',
  timestamp: '2025-07-09 15:07:54'
}
{
  message: '::1 - - [09/Jul/2025:14:07:54 +0000] "GET /favicon.ico HTTP/1.1" 404 - "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-09 15:07:54'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  requestId: 'd1367bfc-4b87-4c7c-8760-b3199e640305',
  method: 'GET',
  url: '/favicon.ico',
  statusCode: 404,
  responseTime: '56ms',
  userId: undefined,
  level: 'warn',
  message: 'Request Error',
  timestamp: '2025-07-09 15:07:54'
}
{
  message: 'WebSocket service initialized',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-09 16:20:26'
}
{
  message: 'Database connected successfully',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-09 16:20:28'
}
{
  message: 'Starting Redis initialization...',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-09 16:20:28'
}
{
  message: 'Redis initialization started (non-blocking)',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-09 16:20:28'
}
{
  message: 'Starting WebSocket service initialization...',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-09 16:20:28'
}
{
  message: 'WebSocket service initialized',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-09 16:20:28'
}
{
  message: 'WebSocket service created successfully',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-09 16:20:28'
}
{
  message: 'Starting Notification service initialization...',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-09 16:20:28'
}
{
  message: 'Notification service created successfully',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-09 16:20:28'
}
{
  message: 'WebSocket and Notification services initialized successfully',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-09 16:20:28'
}
{
  message: 'Server running on port 3000 in development mode',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-09 16:20:28'
}
{
  message: 'API documentation available at http://localhost:3000/api-docs',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-09 16:20:28'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis Client Error (non-critical):',
  timestamp: '2025-07-09 16:20:30'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis initialization failed (continuing without Redis):',
  timestamp: '2025-07-09 16:20:39'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis Client Error (non-critical):',
  timestamp: '2025-07-09 16:20:39'
}
{
  message: 'Redis initialization completed successfully',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-09 16:20:39'
}
{
  message: 'WebSocket service initialized',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-09 20:05:53'
}
{
  message: 'Database connected successfully',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-09 20:05:54'
}
{
  message: 'Starting Redis initialization...',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-09 20:05:54'
}
{
  message: 'Redis initialization started (non-blocking)',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-09 20:05:55'
}
{
  message: 'Starting WebSocket service initialization...',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-09 20:05:55'
}
{
  message: 'WebSocket service initialized',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-09 20:05:55'
}
{
  message: 'WebSocket service created successfully',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-09 20:05:55'
}
{
  message: 'Starting Notification service initialization...',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-09 20:05:55'
}
{
  message: 'Notification service created successfully',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-09 20:05:55'
}
{
  message: 'WebSocket and Notification services initialized successfully',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-09 20:05:55'
}
{
  message: 'Server running on port 3000 in development mode',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-09 20:05:55'
}
{
  message: 'API documentation available at http://localhost:3000/api-docs',
  level: 'info',
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-09 20:05:55'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis Client Error (non-critical):',
  timestamp: '2025-07-09 20:05:59'
}
{
  service: 'bahinlink-api',
  environment: 'development',
  version: '1.0.0',
  level: 'warn',
  message: 'Redis initialization failed (continuing without Redis):',
  timestamp: '2025-07-09 20:06:13'
}
