import React from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Container,
  Typography,
  Button,
  Grid,
  Card,
  CardContent,
  AppBar,
  Toolbar,
  Stack,
  Paper,
} from '@mui/material';
import {
  Security as SecurityIcon,
  Dashboard as DashboardIcon,
  LocationOn as LocationIcon,
  Notifications as NotificationsIcon,
  AdminPanelSettings as AdminIcon,
  Business as ClientIcon,
  PersonAdd as SignupIcon,
} from '@mui/icons-material';

const HomePage: React.FC = () => {
  const navigate = useNavigate();

  const handleAdminLogin = () => {
    navigate('/admin/login');
  };

  const handleClientLogin = () => {
    navigate('/client/login');
  };

  const handleClientSignup = () => {
    navigate('/client/signup');
  };

  return (
    <Box>
      {/* Navigation Bar */}
      <AppBar position="static" elevation={0} sx={{ backgroundColor: 'primary.main' }}>
        <Toolbar>
          <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1 }}>
            <SecurityIcon sx={{ mr: 2, fontSize: 32 }} />
            <Typography variant="h5" component="div" sx={{ fontWeight: 'bold' }}>
              BahinLink
            </Typography>
          </Box>
          <Stack direction="row" spacing={2}>
            <Button
              color="inherit"
              variant="outlined"
              startIcon={<AdminIcon />}
              onClick={handleAdminLogin}
              sx={{ borderColor: 'white', '&:hover': { backgroundColor: 'rgba(255,255,255,0.1)' } }}
            >
              Admin Portal
            </Button>
            <Button
              color="inherit"
              variant="outlined"
              startIcon={<ClientIcon />}
              onClick={handleClientLogin}
              sx={{ borderColor: 'white', '&:hover': { backgroundColor: 'rgba(255,255,255,0.1)' } }}
            >
              Client Portal
            </Button>
          </Stack>
        </Toolbar>
      </AppBar>

      {/* Hero Section */}
      <Box
        sx={{
          background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',
          color: 'white',
          py: 8,
        }}
      >
        <Container maxWidth="lg">
          <Grid container spacing={4} alignItems="center">
            <Grid item xs={12} md={6}>
              <Typography variant="h2" component="h1" gutterBottom sx={{ fontWeight: 'bold' }}>
                Security Workforce Management Made Simple
              </Typography>
              <Typography variant="h6" paragraph sx={{ opacity: 0.9, mb: 4 }}>
                Streamline your security operations with real-time tracking, comprehensive reporting, 
                and intelligent scheduling. Built specifically for security companies like Bahin SARL.
              </Typography>
              <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2}>
                <Button
                  variant="contained"
                  size="large"
                  startIcon={<AdminIcon />}
                  onClick={handleAdminLogin}
                  sx={{
                    backgroundColor: 'white',
                    color: 'primary.main',
                    '&:hover': { backgroundColor: 'grey.100' },
                    py: 1.5,
                    px: 3,
                  }}
                >
                  Access Admin Dashboard
                </Button>
                <Button
                  variant="outlined"
                  size="large"
                  startIcon={<ClientIcon />}
                  onClick={handleClientLogin}
                  sx={{
                    borderColor: 'white',
                    color: 'white',
                    '&:hover': { backgroundColor: 'rgba(255,255,255,0.1)' },
                    py: 1.5,
                    px: 3,
                  }}
                >
                  Client Portal
                </Button>
              </Stack>
            </Grid>
            <Grid item xs={12} md={6}>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Paper elevation={3} sx={{ p: 3, textAlign: 'center', backgroundColor: 'rgba(255,255,255,0.1)' }}>
                    <DashboardIcon sx={{ fontSize: 48, mb: 2, color: 'white' }} />
                    <Typography variant="h6" gutterBottom sx={{ color: 'white' }}>
                      Real-time Analytics
                    </Typography>
                    <Typography variant="body2" sx={{ color: 'rgba(255,255,255,0.8)' }}>
                      Monitor workforce performance with live dashboards
                    </Typography>
                  </Paper>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Paper elevation={3} sx={{ p: 3, textAlign: 'center', backgroundColor: 'rgba(255,255,255,0.1)' }}>
                    <LocationIcon sx={{ fontSize: 48, mb: 2, color: 'white' }} />
                    <Typography variant="h6" gutterBottom sx={{ color: 'white' }}>
                      GPS Tracking
                    </Typography>
                    <Typography variant="body2" sx={{ color: 'rgba(255,255,255,0.8)' }}>
                      Track agent locations and optimize patrol routes
                    </Typography>
                  </Paper>
                </Grid>
                <Grid item xs={12}>
                  <Paper elevation={3} sx={{ p: 3, textAlign: 'center', backgroundColor: 'rgba(255,255,255,0.1)' }}>
                    <NotificationsIcon sx={{ fontSize: 48, mb: 2, color: 'white' }} />
                    <Typography variant="h6" gutterBottom sx={{ color: 'white' }}>
                      Instant Alerts
                    </Typography>
                    <Typography variant="body2" sx={{ color: 'rgba(255,255,255,0.8)' }}>
                      Get real-time notifications for incidents and status updates
                    </Typography>
                  </Paper>
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </Container>
      </Box>

      {/* Features Section */}
      <Container maxWidth="lg" sx={{ py: 8 }}>
        <Typography variant="h3" component="h2" textAlign="center" gutterBottom sx={{ mb: 6 }}>
          Why Choose BahinLink?
        </Typography>
        <Grid container spacing={4}>
          <Grid item xs={12} md={4}>
            <Card elevation={2} sx={{ height: '100%', textAlign: 'center', p: 2 }}>
              <CardContent>
                <SecurityIcon sx={{ fontSize: 64, color: 'primary.main', mb: 2 }} />
                <Typography variant="h5" gutterBottom>
                  Security First
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  Enterprise-grade security with role-based access control, data encryption, 
                  and compliance with GDPR regulations.
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={4}>
            <Card elevation={2} sx={{ height: '100%', textAlign: 'center', p: 2 }}>
              <CardContent>
                <DashboardIcon sx={{ fontSize: 64, color: 'primary.main', mb: 2 }} />
                <Typography variant="h5" gutterBottom>
                  Comprehensive Management
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  Complete workforce management solution with scheduling, attendance tracking, 
                  reporting, and performance analytics.
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={4}>
            <Card elevation={2} sx={{ height: '100%', textAlign: 'center', p: 2 }}>
              <CardContent>
                <LocationIcon sx={{ fontSize: 64, color: 'primary.main', mb: 2 }} />
                <Typography variant="h5" gutterBottom>
                  Mobile Ready
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  Full mobile support for agents in the field with offline capabilities 
                  and real-time synchronization.
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Container>

      {/* Client Access Section */}
      <Box sx={{ backgroundColor: 'grey.50', py: 8 }}>
        <Container maxWidth="md">
          <Typography variant="h4" component="h2" textAlign="center" gutterBottom>
            New Client?
          </Typography>
          <Typography variant="h6" textAlign="center" color="text.secondary" paragraph>
            Join BahinLink to get real-time visibility into your security services
          </Typography>
          <Box sx={{ textAlign: 'center', mt: 4 }}>
            <Button
              variant="contained"
              size="large"
              startIcon={<SignupIcon />}
              onClick={handleClientSignup}
              sx={{ py: 1.5, px: 4, mr: 2 }}
            >
              Create Client Account
            </Button>
            <Button
              variant="outlined"
              size="large"
              startIcon={<ClientIcon />}
              onClick={handleClientLogin}
              sx={{ py: 1.5, px: 4 }}
            >
              Existing Client Login
            </Button>
          </Box>
        </Container>
      </Box>

      {/* Footer */}
      <Box sx={{ backgroundColor: 'grey.900', color: 'white', py: 6 }}>
        <Container maxWidth="lg">
          <Grid container spacing={4}>
            <Grid item xs={12} md={4}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <SecurityIcon sx={{ mr: 2, fontSize: 32 }} />
                <Typography variant="h5" component="div" sx={{ fontWeight: 'bold' }}>
                  BahinLink
                </Typography>
              </Box>
              <Typography variant="body2" color="grey.400">
                Empowering security companies with intelligent workforce management solutions.
              </Typography>
            </Grid>
            <Grid item xs={12} md={4}>
              <Typography variant="h6" gutterBottom>
                Quick Access
              </Typography>
              <Stack spacing={1}>
                <Button
                  color="inherit"
                  onClick={handleAdminLogin}
                  sx={{ justifyContent: 'flex-start', textTransform: 'none' }}
                >
                  Admin Dashboard
                </Button>
                <Button
                  color="inherit"
                  onClick={handleClientLogin}
                  sx={{ justifyContent: 'flex-start', textTransform: 'none' }}
                >
                  Client Portal
                </Button>
              </Stack>
            </Grid>
            <Grid item xs={12} md={4}>
              <Typography variant="h6" gutterBottom>
                Support
              </Typography>
              <Stack spacing={1}>
                <Button
                  color="inherit"
                  sx={{ justifyContent: 'flex-start', textTransform: 'none' }}
                >
                  Documentation
                </Button>
                <Button
                  color="inherit"
                  sx={{ justifyContent: 'flex-start', textTransform: 'none' }}
                >
                  Help Center
                </Button>
              </Stack>
            </Grid>
          </Grid>
          <Box sx={{ borderTop: 1, borderColor: 'grey.700', mt: 4, pt: 4, textAlign: 'center' }}>
            <Typography variant="body2" color="grey.400">
              © 2024 BahinLink. All rights reserved.
            </Typography>
          </Box>
        </Container>
      </Box>
    </Box>
  );
};

export default HomePage;
