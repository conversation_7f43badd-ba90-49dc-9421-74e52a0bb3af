{"version": 3, "file": "emergencyResponseService.js", "sourceRoot": "", "sources": ["../../src/services/emergencyResponseService.ts"], "names": [], "mappings": ";;;AAAA,2CAA8C;AAC9C,4CAAyC;AAEzC,MAAM,MAAM,GAAG,IAAI,qBAAY,EAAE,CAAC;AAkClC,MAAa,wBAAwB;IAGnC;QACE,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IAED,KAAK,CAAC,oBAAoB,CAAC,MAAc;QACvC,IAAI;YACF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC;gBAC3D,KAAK,EAAE,EAAE,MAAM,EAAE;gBACjB,OAAO,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;aACzB,CAAC,CAAC;YAEH,OAAO,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;gBAC9B,EAAE,EAAE,OAAO,CAAC,EAAE;gBACd,IAAI,EAAE,OAAO,CAAC,IAAI;gBAClB,MAAM,EAAE,OAAO,CAAC,MAAM;gBACtB,KAAK,EAAE,OAAO,CAAC,KAAK;gBACpB,QAAQ,EAAE,OAAO,CAAC,QAAQ;aAC3B,CAAC,CAAC,CAAC;SACL;QAAC,OAAO,KAAK,EAAE;YACd,eAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;YAC1D,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;SACvD;IACH,CAAC;IAED,KAAK,CAAC,sBAAsB,CAAC,MAAc;QACzC,IAAI;YACF,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,QAAQ,CAAC;gBAC/D,KAAK,EAAE,EAAE,MAAM,EAAE;aAClB,CAAC,CAAC;YAEH,OAAO,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;gBAClC,EAAE,EAAE,SAAS,CAAC,EAAE;gBAChB,IAAI,EAAE,SAAS,CAAC,IAAI;gBACpB,MAAM,EAAE,SAAS,CAAC,MAAM;gBACxB,KAAK,EAAE,SAAS,CAAC,KAAK;aACvB,CAAC,CAAC,CAAC;SACL;QAAC,OAAO,KAAK,EAAE;YACd,eAAM,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;YAC5D,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;SACzD;IACH,CAAC;IAED,KAAK,CAAC,oBAAoB,CAAC,KAK1B;QACC,IAAI;YACF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC;gBACvD,IAAI,EAAE;oBACJ,MAAM,EAAE,KAAK,CAAC,MAAM;oBACpB,OAAO,EAAE,KAAK,CAAC,OAAO;oBACtB,IAAI,EAAE,KAAK,CAAC,IAAI;oBAChB,OAAO,EAAE,KAAK,CAAC,OAAO;iBACvB;aACF,CAAC,CAAC;YAEH,OAAO;gBACL,EAAE,EAAE,QAAQ,CAAC,EAAE;gBACf,MAAM,EAAE,QAAQ,CAAC,MAAM;gBACvB,OAAO,EAAE,QAAQ,CAAC,OAAO;gBACzB,IAAI,EAAE,QAAQ,CAAC,IAAI;gBACnB,OAAO,EAAE,QAAQ,CAAC,OAAO;gBACzB,SAAS,EAAE,QAAQ,CAAC,SAAS;aAC9B,CAAC;SACH;QAAC,OAAO,KAAK,EAAE;YACd,eAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;YACvD,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;SACrD;IACH,CAAC;IAED,KAAK,CAAC,uBAAuB,CAAC,QAI7B;QACC,IAAI;YACF,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,MAAM,CAAC;gBAC7D,IAAI,EAAE;oBACJ,OAAO,EAAE,QAAQ,CAAC,OAAO;oBACzB,WAAW,EAAE,QAAQ,CAAC,WAAW;oBACjC,QAAQ,EAAE,QAAQ,CAAC,QAAQ;iBAC5B;aACF,CAAC,CAAC;YAEH,OAAO;gBACL,EAAE,EAAE,WAAW,CAAC,EAAE;gBAClB,OAAO,EAAE,WAAW,CAAC,OAAO;gBAC5B,WAAW,EAAE,WAAW,CAAC,WAAW;gBACpC,QAAQ,EAAE,WAAW,CAAC,QAAQ;gBAC9B,SAAS,EAAE,WAAW,CAAC,SAAS;aACjC,CAAC;SACH;QAAC,OAAO,KAAK,EAAE;YACd,eAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;YAC1D,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;SACxD;IACH,CAAC;IAED,KAAK,CAAC,kBAAkB,CAAC,MAAc,EAAE,UAAkB,EAAE,QAAgB,EAAE,MAAc,EAAE,OAAY;QACzG,IAAI;YACF,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;gBAChC,IAAI,EAAE;oBACJ,MAAM;oBACN,SAAS,EAAE,UAAU;oBACrB,QAAQ,EAAE,QAAQ;oBAClB,MAAM;oBACN,OAAO;oBACP,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB;aACF,CAAC,CAAC;SACJ;QAAC,OAAO,KAAK,EAAE;YACd,eAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;SACxD;IACH,CAAC;IAED,KAAK,CAAC,uBAAuB,CAAC,QAA4B,EAAE,KAAqB;QAC/E,IAAI;YACF,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE;gBAC9B,IAAI;oBACF,eAAM,CAAC,IAAI,CAAC,+BAA+B,OAAO,CAAC,IAAI,OAAO,OAAO,CAAC,KAAK,gBAAgB,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC;iBACxG;gBAAC,OAAO,KAAK,EAAE;oBACd,eAAM,CAAC,KAAK,CAAC,4BAA4B,OAAO,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;iBAChE;aACF;SACF;QAAC,OAAO,KAAK,EAAE;YACd,eAAM,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;SAC5D;IACH,CAAC;IAED,KAAK,CAAC,sBAAsB,CAAC,MAAc;QACzC,IAAI;YAEF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC;gBAC9C,KAAK,EAAE;oBACL,MAAM;oBACN,MAAM,EAAE,aAAoB;iBAC7B;gBACD,MAAM,EAAE;oBACN,OAAO,EAAE,IAAI;iBACd;aACF,CAAC,CAAC;YAEH,MAAM,QAAQ,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,OAAO,CAAa,CAAC;YAExE,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;gBACzB,OAAO,EAAE,CAAC;aACX;YAED,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC;gBAC9C,KAAK,EAAE;oBACL,EAAE,EAAE,EAAE,EAAE,EAAE,QAAQ,EAAE;iBACrB;gBACD,MAAM,EAAE;oBACN,EAAE,EAAE,IAAI;oBACR,MAAM,EAAE,IAAI;iBACb;aACF,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;YAClD,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;gBAC5C,KAAK,EAAE;oBACL,EAAE,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE;iBACpB;gBACD,MAAM,EAAE;oBACN,EAAE,EAAE,IAAI;oBACR,SAAS,EAAE,IAAI;oBACf,QAAQ,EAAE,IAAI;oBACd,QAAQ,EAAE,IAAI;oBACd,KAAK,EAAE,IAAI;oBACX,KAAK,EAAE,IAAI;iBACZ;aACF,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;YAE5D,OAAO,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;gBACxB,MAAM,IAAI,GAAG,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;gBACvC,OAAO;oBACL,EAAE,EAAE,KAAK,CAAC,EAAE;oBACZ,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,IAAI,EAAE,IAAI,IAAI,CAAC,QAAQ,IAAI,EAAE,EAAE,CAAC,IAAI,EAAE,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS;oBACjG,KAAK,EAAE,IAAI,EAAE,KAAK,IAAI,IAAI;oBAC1B,KAAK,EAAE,IAAI,EAAE,KAAK,IAAI,IAAI;oBAC1B,eAAe,EAAE,IAAI;iBACtB,CAAC;YACJ,CAAC,CAAC,CAAC;SACJ;QAAC,OAAO,KAAK,EAAE;YACd,eAAM,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;YAC5D,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;SACzD;IACH,CAAC;IAED,KAAK,CAAC,uBAAuB,CAAC,UAAkB,EAAE,OAEjD;QACC,IAAI;YACF,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,MAAM,CAAC;gBACjE,KAAK,EAAE,EAAE,EAAE,EAAE,UAAU,EAAE;gBACzB,IAAI,EAAE,OAAO;aACd,CAAC,CAAC;YAEH,OAAO;gBACL,EAAE,EAAE,eAAe,CAAC,EAAE;gBACtB,OAAO,EAAE,eAAe,CAAC,OAAO;gBAChC,WAAW,EAAE,eAAe,CAAC,WAAW;gBACxC,QAAQ,EAAE,eAAe,CAAC,QAAQ;gBAClC,SAAS,EAAE,eAAe,CAAC,SAAS;aACrC,CAAC;SACH;QAAC,OAAO,KAAK,EAAE;YACd,eAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;YAC1D,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;SACxD;IACH,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,OAAe,EAAE,eAAuB,EAAE,MAAc;QAC9E,IAAI;YACF,MAAM,IAAI,CAAC,kBAAkB,CAAC,UAAU,EAAE,gBAAgB,EAAE,OAAO,EAAE,QAAQ,EAAE;gBAC7E,eAAe;gBACf,MAAM;gBACN,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC,CAAC;YAEH,eAAM,CAAC,IAAI,CAAC,aAAa,OAAO,uBAAuB,eAAe,KAAK,MAAM,EAAE,CAAC,CAAC;SACtF;QAAC,OAAO,KAAK,EAAE;YACd,eAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;YACnD,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;SACjD;IACH,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,OAAe,EAAE,eAAuB,EAAE,UAAkB;QACjF,IAAI;YACF,MAAM,IAAI,CAAC,kBAAkB,CAAC,SAAS,EAAE,gBAAgB,EAAE,OAAO,EAAE,UAAU,EAAE;gBAC9E,eAAe;gBACf,UAAU,EAAE,IAAI,IAAI,EAAE;aACvB,CAAC,CAAC;YAEH,eAAM,CAAC,IAAI,CAAC,aAAa,OAAO,gBAAgB,UAAU,EAAE,CAAC,CAAC;SAC/D;QAAC,OAAO,KAAK,EAAE;YACd,eAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YAClD,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;SAChD;IACH,CAAC;IAED,KAAK,CAAC,mBAAmB,CAAC,MAAc,EAAE,SAAe,EAAE,OAAa;QACtE,IAAI;YACF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC;gBACvD,KAAK,EAAE;oBACL,MAAM;oBACN,SAAS,EAAE;wBACT,GAAG,EAAE,SAAS;wBACd,GAAG,EAAE,OAAO;qBACb;iBACF;gBACD,MAAM,EAAE;oBACN,EAAE,EAAE,IAAI;oBACR,IAAI,EAAE,IAAI;oBACV,OAAO,EAAE,IAAI;oBACb,SAAS,EAAE,IAAI;oBACf,OAAO,EAAE,IAAI;iBACd;gBACD,OAAO,EAAE;oBACP,SAAS,EAAE,MAAM;iBAClB;aACF,CAAC,CAAC;YAGH,MAAM,QAAQ,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YACpD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC;gBAC9C,KAAK,EAAE;oBACL,EAAE,EAAE,EAAE,EAAE,EAAE,QAAQ,EAAE;iBACrB;gBACD,MAAM,EAAE;oBACN,EAAE,EAAE,IAAI;oBACR,MAAM,EAAE,IAAI;iBACb;aACF,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;YAClD,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;gBAC5C,KAAK,EAAE;oBACL,EAAE,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE;iBACpB;gBACD,MAAM,EAAE;oBACN,EAAE,EAAE,IAAI;oBACR,SAAS,EAAE,IAAI;oBACf,QAAQ,EAAE,IAAI;oBACd,QAAQ,EAAE,IAAI;iBACf;aACF,CAAC,CAAC;YAEH,MAAM,QAAQ,GAAG,IAAI,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;YACjE,MAAM,OAAO,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;YAE5D,OAAO,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;gBACxB,MAAM,KAAK,GAAG,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;gBAC1C,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;gBAEtD,OAAO;oBACL,EAAE,EAAE,KAAK,CAAC,EAAE;oBACZ,IAAI,EAAE,KAAK,CAAC,IAAI;oBAChB,OAAO,EAAE,KAAK,CAAC,OAAO;oBACtB,SAAS,EAAE,KAAK,CAAC,SAAS;oBAC1B,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;wBACb,EAAE,EAAE,KAAK,CAAC,EAAE;wBACZ,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,IAAI,EAAE,IAAI,IAAI,CAAC,QAAQ,IAAI,EAAE,EAAE,CAAC,IAAI,EAAE,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS;qBAClG,CAAC,CAAC,CAAC,IAAI;iBACT,CAAC;YACJ,CAAC,CAAC,CAAC;SACJ;QAAC,OAAO,KAAK,EAAE;YACd,eAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;YACzD,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;SACtD;IACH,CAAC;IAED,KAAK,CAAC,sBAAsB,CAAC,MAAc,EAAE,MAAc;QACzD,IAAI;YACF,MAAM,SAAS,GAAG,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;YACrD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC;gBACvD,KAAK,EAAE;oBACL,MAAM;oBACN,SAAS,EAAE;wBACT,GAAG,EAAE,SAAS;qBACf;iBACF;aACF,CAAC,CAAC;YAEH,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC;YAClC,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE;gBAChD,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;gBAC7C,OAAO,GAAG,CAAC;YACb,CAAC,EAAE,EAA4B,CAAC,CAAC;YAEjC,OAAO;gBACL,WAAW;gBACX,YAAY;gBACZ,mBAAmB,EAAE,CAAC;gBACtB,WAAW,EAAE,CAAC;gBACd,MAAM;aACP,CAAC;SACH;QAAC,OAAO,KAAK,EAAE;YACd,eAAM,CAAC,KAAK,CAAC,yCAAyC,EAAE,KAAK,CAAC,CAAC;YAC/D,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;SAC7D;IACH,CAAC;IAEO,qBAAqB,CAAC,MAAc;QAC1C,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,QAAQ,MAAM,EAAE;YACd,KAAK,KAAK;gBACR,OAAO,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;YACvD,KAAK,IAAI;gBACP,OAAO,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;YAC3D,KAAK,KAAK;gBACR,OAAO,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;YAC5D,KAAK,KAAK;gBACR,OAAO,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;YAC5D;gBACE,OAAO,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;SACxD;IACH,CAAC;CACF;AA3WD,4DA2WC"}