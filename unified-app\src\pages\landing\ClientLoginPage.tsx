import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth, useUser, SignIn } from '@clerk/clerk-react';
import {
  Box,
  Container,
  Typography,
  Card,
  CardContent,
  Button,
  AppBar,
  Toolbar,
  CircularProgress,
  Grid,
} from '@mui/material';
import {
  Security as SecurityIcon,
  ArrowBack as ArrowBackIcon,
  Business as ClientIcon,
  PersonAdd as SignupIcon,
} from '@mui/icons-material';

const ClientLoginPage: React.FC = () => {
  const navigate = useNavigate();
  const { isSignedIn, isLoaded } = useAuth();
  const { user } = useUser();

  useEffect(() => {
    if (isLoaded && isSignedIn && user) {
      // Check if user has client role (or default to client)
      const userRole = user.publicMetadata?.role;
      if (userRole === 'CLIENT' || userRole === 'client' || userRole === 'user' || !userRole) {
        // Redirect to client dashboard in unified app
        navigate('/client/dashboard');
      } else {
        // User doesn't have client privileges
        console.warn('User does not have client privileges');
        // Could show an error message or redirect back
      }
    }
  }, [isLoaded, isSignedIn, user, navigate]);

  const handleBackToHome = () => {
    navigate('/');
  };

  const handleSignup = () => {
    navigate('/client/signup');
  };

  if (!isLoaded) {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          minHeight: '100vh',
        }}
      >
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ minHeight: '100vh', backgroundColor: 'grey.50' }}>
      {/* Navigation Bar */}
      <AppBar position="static" elevation={0} sx={{ backgroundColor: 'primary.main' }}>
        <Toolbar>
          <Button
            color="inherit"
            startIcon={<ArrowBackIcon />}
            onClick={handleBackToHome}
            sx={{ mr: 2 }}
          >
            Back to Home
          </Button>
          <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1 }}>
            <SecurityIcon sx={{ mr: 2, fontSize: 32 }} />
            <Typography variant="h5" component="div" sx={{ fontWeight: 'bold' }}>
              BahinLink
            </Typography>
          </Box>
        </Toolbar>
      </AppBar>

      {/* Login Content */}
      <Container maxWidth="md" sx={{ py: 8 }}>
        <Grid container spacing={4}>
          <Grid item xs={12} md={6}>
            <Card elevation={3} sx={{ height: '100%' }}>
              <CardContent sx={{ p: 4 }}>
                <Box sx={{ textAlign: 'center', mb: 4 }}>
                  <ClientIcon
                    sx={{
                      fontSize: 64,
                      color: 'primary.main',
                      mb: 2,
                    }}
                  />
                  <Typography variant="h4" gutterBottom sx={{ fontWeight: 'bold' }}>
                    Client Portal
                  </Typography>
                  <Typography variant="subtitle1" color="text.secondary">
                    Sign in to monitor your security services
                  </Typography>
                </Box>

                {/* Clerk SignIn Component */}
                <Box sx={{ display: 'flex', justifyContent: 'center' }}>
                  <SignIn
                    routing="hash"
                    signUpUrl="/client/signup"
                    afterSignInUrl="/client/dashboard"
                    appearance={{
                      elements: {
                        formButtonPrimary: {
                          backgroundColor: '#1976d2',
                          '&:hover': {
                            backgroundColor: '#1565c0',
                          },
                        },
                      },
                    }}
                  />
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={6}>
            <Card elevation={3} sx={{ height: '100%', backgroundColor: 'primary.main', color: 'white' }}>
              <CardContent sx={{ p: 4, height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                <Box sx={{ textAlign: 'center' }}>
                  <SignupIcon sx={{ fontSize: 64, mb: 3 }} />
                  <Typography variant="h4" gutterBottom sx={{ fontWeight: 'bold' }}>
                    New Client?
                  </Typography>
                  <Typography variant="h6" paragraph sx={{ opacity: 0.9 }}>
                    Get real-time visibility into your security services
                  </Typography>
                  
                  <Box sx={{ mb: 4 }}>
                    <Typography variant="body1" paragraph>
                      • Monitor security personnel in real-time
                    </Typography>
                    <Typography variant="body1" paragraph>
                      • Access detailed patrol and incident reports
                    </Typography>
                    <Typography variant="body1" paragraph>
                      • Request additional services instantly
                    </Typography>
                    <Typography variant="body1" paragraph>
                      • Communicate directly with your security team
                    </Typography>
                  </Box>

                  <Button
                    variant="contained"
                    size="large"
                    startIcon={<SignupIcon />}
                    onClick={handleSignup}
                    sx={{
                      backgroundColor: 'white',
                      color: 'primary.main',
                      '&:hover': { backgroundColor: 'grey.100' },
                      py: 1.5,
                      px: 4,
                    }}
                  >
                    Create Account
                  </Button>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Additional Information */}
        <Box sx={{ mt: 4, textAlign: 'center' }}>
          <Typography variant="body2" color="text.secondary">
            Need help accessing your account?{' '}
            <Button
              color="primary"
              sx={{ textTransform: 'none', textDecoration: 'underline', p: 0 }}
            >
              Contact Support
            </Button>
          </Typography>
        </Box>
      </Container>
    </Box>
  );
};

export default ClientLoginPage;
