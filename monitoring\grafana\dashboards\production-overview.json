{"dashboard": {"id": null, "title": "BahinLink Production Overview", "tags": ["production", "overview", "bahinlink"], "timezone": "browser", "panels": [{"id": 1, "title": "System Health Score", "type": "gauge", "targets": [{"expr": "bahinlink_system_health_score", "legendFormat": "Health Score"}], "fieldConfig": {"defaults": {"min": 0, "max": 100, "unit": "percent", "color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 70}, {"color": "green", "value": 90}]}}}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 0}}, {"id": 2, "title": "Service Availability", "type": "stat", "targets": [{"expr": "avg(up{job=~\"bahinlink.*\"})", "legendFormat": "Availability"}], "fieldConfig": {"defaults": {"unit": "percentunit", "color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 0.95}, {"color": "green", "value": 0.99}]}}}, "gridPos": {"h": 4, "w": 6, "x": 6, "y": 0}}, {"id": 3, "title": "Request Rate", "type": "stat", "targets": [{"expr": "sum(rate(http_requests_total{job=\"bahinlink-backend\"}[5m]))", "legendFormat": "Requests/sec"}], "fieldConfig": {"defaults": {"unit": "reqps", "color": {"mode": "palette-classic"}}}, "gridPos": {"h": 4, "w": 6, "x": 12, "y": 0}}, {"id": 4, "title": "Error Rate", "type": "stat", "targets": [{"expr": "sum(rate(http_requests_total{job=\"bahinlink-backend\",status=~\"5..\"}[5m])) / sum(rate(http_requests_total{job=\"bahinlink-backend\"}[5m]))", "legendFormat": "Error Rate"}], "fieldConfig": {"defaults": {"unit": "percentunit", "color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 0.01}, {"color": "red", "value": 0.05}]}}}, "gridPos": {"h": 4, "w": 6, "x": 18, "y": 0}}, {"id": 5, "title": "Response Time Percentiles", "type": "graph", "targets": [{"expr": "histogram_quantile(0.50, rate(http_request_duration_seconds_bucket{job=\"bahinlink-backend\"}[5m]))", "legendFormat": "50th percentile"}, {"expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job=\"bahinlink-backend\"}[5m]))", "legendFormat": "95th percentile"}, {"expr": "histogram_quantile(0.99, rate(http_request_duration_seconds_bucket{job=\"bahinlink-backend\"}[5m]))", "legendFormat": "99th percentile"}], "yAxes": [{"label": "Response Time (seconds)", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 6, "y": 4}}, {"id": 6, "title": "Active Users", "type": "stat", "targets": [{"expr": "bahinlink_active_users_total", "legendFormat": "Active Users"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}}}, "gridPos": {"h": 4, "w": 6, "x": 18, "y": 4}}, {"id": 7, "title": "Database Performance", "type": "graph", "targets": [{"expr": "pg_stat_database_tup_fetched{datname=\"bahinlink\"}", "legendFormat": "<PERSON><PERSON> Fetched"}, {"expr": "pg_stat_database_tup_inserted{datname=\"bahinlink\"}", "legendFormat": "<PERSON><PERSON>"}, {"expr": "pg_stat_database_tup_updated{datname=\"bahinlink\"}", "legendFormat": "<PERSON><PERSON>"}], "yAxes": [{"label": "Operations/sec", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}}, {"id": 8, "title": "Memory Usage", "type": "graph", "targets": [{"expr": "process_resident_memory_bytes{job=\"bahinlink-backend\"}", "legendFormat": "Backend Memory"}, {"expr": "pg_stat_database_size{datname=\"bahinlink\"}", "legendFormat": "Database Size"}], "yAxes": [{"label": "Memory (bytes)", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}}, {"id": 9, "title": "Business Metrics", "type": "table", "targets": [{"expr": "bahinlink_active_agents_total", "legendFormat": "Active Agents", "format": "table"}, {"expr": "bahinlink_active_shifts_total", "legendFormat": "Active Shifts", "format": "table"}, {"expr": "bahinlink_open_incidents_total", "legendFormat": "Open Incidents", "format": "table"}, {"expr": "bahinlink_completed_reports_today", "legendFormat": "Reports Today", "format": "table"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}}, {"id": 10, "title": "Alert <PERSON>", "type": "alertlist", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}}], "time": {"from": "now-1h", "to": "now"}, "refresh": "30s", "templating": {"list": [{"name": "environment", "type": "constant", "current": {"value": "production"}}]}}}