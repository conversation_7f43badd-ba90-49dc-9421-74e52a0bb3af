import React, { useEffect } from 'react';
import { useAuth, useUser } from '@clerk/clerk-react';
import { Box, CircularProgress, Typography } from '@mui/material';

const AdminRedirect: React.FC = () => {
  const { isSignedIn, isLoaded } = useAuth();
  const { user } = useUser();

  useEffect(() => {
    if (isLoaded) {
      if (isSignedIn && user) {
        // Check if user has admin role (you can customize this logic)
        const userRole = user.publicMetadata?.role || 'user';
        
        if (userRole === 'admin' || userRole === 'supervisor' || userRole === 'ADMIN' || userRole === 'SUPERVISOR') {
          // Redirect to admin portal with the current path
          const currentPath = window.location.pathname.replace('/admin', '');
          const adminUrl = process.env.REACT_APP_ADMIN_PORTAL_URL || 'http://localhost:3001';
          window.location.href = `${adminUrl}${currentPath || '/dashboard'}`;
        } else {
          // Redirect to admin login if not admin
          window.location.href = '/admin/login';
        }
      } else {
        // Redirect to admin login if not signed in
        window.location.href = '/admin/login';
      }
    }
  }, [isLoaded, isSignedIn, user]);

  if (!isLoaded) {
    return (
      <Box
        display="flex"
        flexDirection="column"
        alignItems="center"
        justifyContent="center"
        minHeight="100vh"
        gap={2}
      >
        <CircularProgress size={60} />
        <Typography variant="h6" color="textSecondary">
          Loading Admin Portal...
        </Typography>
      </Box>
    );
  }

  return (
    <Box
      display="flex"
      flexDirection="column"
      alignItems="center"
      justifyContent="center"
      minHeight="100vh"
      gap={2}
    >
      <CircularProgress size={60} />
      <Typography variant="h6" color="textSecondary">
        Redirecting to Admin Portal...
      </Typography>
    </Box>
  );
};

export default AdminRedirect;
