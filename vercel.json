{"version": 2, "name": "bahinlink-admin-portal", "outputDirectory": "admin-portal/build", "builds": [{"src": "admin-portal/package.json", "use": "@vercel/static-build", "config": {"distDir": "admin-portal/build", "buildCommand": "cd admin-portal && npm install && npm run build"}}, {"src": "client-portal/package.json", "use": "@vercel/static-build", "config": {"distDir": "client-portal/build", "buildCommand": "cd client-portal && npm install && npm run build"}}, {"src": "backend/server.js", "use": "@vercel/node", "config": {"includeFiles": ["backend/src/**"]}}], "rewrites": [{"source": "/api/(.*)", "destination": "/backend/server.js"}, {"source": "/client/(.*)", "destination": "/client-portal/build/$1"}, {"source": "/static/(.*)", "destination": "/admin-portal/build/static/$1"}, {"source": "/(.*)", "destination": "/admin-portal/build/index.html"}]}