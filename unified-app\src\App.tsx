import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { CssBaseline, Box } from '@mui/material';
import { Provider } from 'react-redux';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { ClerkProvider } from '@clerk/clerk-react';

import { store } from './store';
import ErrorBoundary from './components/common/ErrorBoundary';
import { SocketProvider } from './providers/SocketProvider';
import { NotificationProvider } from './providers/NotificationProvider';

// Landing Pages
import HomePage from './pages/landing/HomePage';
import AdminLoginPage from './pages/landing/AdminLoginPage';
import ClientLoginPage from './pages/landing/ClientLoginPage';
import ClientSignupPage from './pages/landing/ClientSignupPage';

// Admin Portal Routes
import AdminPortal from './pages/admin/AdminPortal';

// Client Portal Routes  
import ClientPortal from './pages/client/ClientPortal';

// Authentication Guards
import AdminAuthGuard from './components/auth/AdminAuthGuard';
import ClientAuthGuard from './components/auth/ClientAuthGuard';

// Get Clerk publishable key from environment
const clerkPubKey = process.env.REACT_APP_CLERK_PUBLISHABLE_KEY;

if (!clerkPubKey) {
  throw new Error('Missing Clerk Publishable Key');
}

// Create Material-UI theme
const theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2',
      light: '#42a5f5',
      dark: '#1565c0',
    },
    secondary: {
      main: '#dc004e',
      light: '#ff5983',
      dark: '#9a0036',
    },
    background: {
      default: '#f5f5f5',
      paper: '#ffffff',
    },
  },
  typography: {
    fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
    h1: {
      fontSize: '2.5rem',
      fontWeight: 600,
    },
    h2: {
      fontSize: '2rem',
      fontWeight: 600,
    },
    h3: {
      fontSize: '1.75rem',
      fontWeight: 600,
    },
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none',
          borderRadius: 8,
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: 12,
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        },
      },
    },
  },
});

const App: React.FC = () => {
  return (
    <ClerkProvider publishableKey={clerkPubKey}>
      <Provider store={store}>
        <ThemeProvider theme={theme}>
          <LocalizationProvider dateAdapter={AdapterDateFns}>
            <CssBaseline />
            <Router>
              <ErrorBoundary>
                <SocketProvider>
                  <NotificationProvider>
                    <Box sx={{ minHeight: '100vh' }}>
                      <Routes>
                        {/* Landing Page Routes */}
                        <Route path="/" element={<HomePage />} />
                        <Route path="/admin/login" element={<AdminLoginPage />} />
                        <Route path="/client/login" element={<ClientLoginPage />} />
                        <Route path="/client/signup" element={<ClientSignupPage />} />

                        {/* Admin Portal Routes - All /admin/* routes */}
                        <Route 
                          path="/admin/*" 
                          element={
                            <AdminAuthGuard>
                              <AdminPortal />
                            </AdminAuthGuard>
                          } 
                        />

                        {/* Client Portal Routes - All /client/* routes */}
                        <Route 
                          path="/client/*" 
                          element={
                            <ClientAuthGuard>
                              <ClientPortal />
                            </ClientAuthGuard>
                          } 
                        />

                        {/* Fallback route */}
                        <Route path="*" element={<Navigate to="/" replace />} />
                      </Routes>
                    </Box>
                  </NotificationProvider>
                </SocketProvider>
              </ErrorBoundary>
            </Router>
          </LocalizationProvider>
        </ThemeProvider>
      </Provider>
    </ClerkProvider>
  );
};

export default App;
