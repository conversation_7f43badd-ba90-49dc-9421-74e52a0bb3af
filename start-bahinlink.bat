@echo off
title BahinLink System Startup

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    BahinLink System Startup                 ║
echo ║          Security Workforce Management Platform             ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔍 Checking Node.js installation...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js is not installed or not in PATH
    echo Please install Node.js 18+ from https://nodejs.org
    pause
    exit /b 1
)

echo ✅ Node.js is installed
echo.

echo 📦 Installing dependencies...
call npm run install:all
if errorlevel 1 (
    echo ❌ Failed to install dependencies
    pause
    exit /b 1
)

echo.
echo 🔧 Setting up database...
call npm run db:setup
if errorlevel 1 (
    echo ❌ Failed to setup database
    echo Make sure PostgreSQL is running or use Docker
    pause
    exit /b 1
)

echo.
echo 🚀 Starting BahinLink System...
echo.
echo 📍 Application URLs:
echo    • Landing Page:  http://localhost:3000
echo    • Admin Portal:  http://localhost:3001  
echo    • Client Portal: http://localhost:3002
echo    • Backend API:   http://localhost:8000
echo.
echo 💡 Press Ctrl+C to stop all services
echo.

call npm start

echo.
echo 👋 BahinLink System stopped
pause
