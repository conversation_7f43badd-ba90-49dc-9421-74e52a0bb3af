# Agent System Environment Configuration
# Copy this file to .env and update the values for your environment

# =============================================================================
# GENERAL CONFIGURATION
# =============================================================================
NODE_ENV=development
PORT=3003
HOST=localhost

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# PostgreSQL Database URL
# Format: postgresql://username:password@localhost:5432/database_name
DATABASE_URL=postgresql://postgres:password@localhost:5432/agent_system

# Database Pool Configuration
DB_POOL_MIN=2
DB_POOL_MAX=10
DB_TIMEOUT=30000

# Database Backup Configuration
DB_BACKUP_ENABLED=false
DB_BACKUP_SCHEDULE="0 2 * * *"
DB_BACKUP_RETENTION=30

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_KEY_PREFIX=agent_system:

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-in-production-min-32-chars
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d
JWT_ISSUER=agent-system
JWT_AUDIENCE=agent-system-users

# Encryption Configuration
ENCRYPTION_KEY=your-32-character-encryption-key-here
ENCRYPTION_ALGORITHM=aes-256-gcm

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS Configuration
CORS_ORIGIN=http://localhost:3000,http://localhost:3001
CORS_CREDENTIALS=true

# =============================================================================
# EXTERNAL API CONFIGURATION
# =============================================================================
# Weather API (OpenWeatherMap)
WEATHER_API_KEY=your_openweathermap_api_key_here
WEATHER_API_URL=https://api.openweathermap.org/data/2.5

# Google Maps API (for traffic data)
GOOGLE_MAPS_API_KEY=your_google_maps_api_key_here
GOOGLE_MAPS_API_URL=https://maps.googleapis.com/maps/api

# Security Alerts API
SECURITY_API_KEY=your_security_api_key_here
SECURITY_API_URL=https://api.security-provider.com

# Market Data API
MARKET_DATA_API_KEY=your_market_data_api_key_here
MARKET_DATA_API_URL=https://api.marketdata.com/v1

# Cryptocurrency API (CoinGecko - no key required)
CRYPTO_API_URL=https://api.coingecko.com/api/v3

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
LOG_LEVEL=info
LOG_FORMAT=json
LOG_MAX_FILES=5
LOG_MAX_SIZE=20m
LOG_DATE_PATTERN=YYYY-MM-DD
LOG_ZIPPED_ARCHIVE=true
LOG_DIRECTORY=logs

# =============================================================================
# MONITORING AND ALERTING
# =============================================================================
# Health Check Configuration
HEALTH_CHECK_INTERVAL=30000
HEALTH_CHECK_TIMEOUT=5000
HEALTH_CHECK_RETRIES=3

# Memory and Performance Thresholds
MEMORY_THRESHOLD=85
DISK_THRESHOLD=90
CPU_THRESHOLD=80
RESPONSE_TIME_THRESHOLD=2000

# Alerting Configuration
ALERTING_ENABLED=false

# Slack Alerts
SLACK_ALERTS_ENABLED=false
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
SLACK_CHANNEL=#alerts
SLACK_USERNAME=Agent System Monitor

# Email Alerts
EMAIL_ALERTS_ENABLED=false
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
ALERT_EMAIL_FROM=<EMAIL>
ALERT_EMAIL_TO=<EMAIL>,<EMAIL>

# PagerDuty Alerts
PAGERDUTY_ALERTS_ENABLED=false
PAGERDUTY_INTEGRATION_KEY=your_pagerduty_integration_key

# =============================================================================
# PERFORMANCE CONFIGURATION
# =============================================================================
# Cache Configuration
CACHE_TTL=300000
CACHE_MAX_SIZE=1000
CACHE_CHECK_PERIOD=60000

# Query Performance
SLOW_QUERY_THRESHOLD=1000
MAX_QUERY_TIME=30000
CONNECTION_POOL_SIZE=10

# =============================================================================
# BUSINESS INTELLIGENCE CONFIGURATION
# =============================================================================
# BI Cache Settings
BI_CACHE_TTL=300000
BI_CACHE_MAX_SIZE=200

# Report Generation
BI_MAX_DATA_POINTS=10000
BI_REPORT_TIMEOUT=30000
BI_CONCURRENCY=5
BI_RETRY_ATTEMPTS=2

# Forecasting
BI_FORECASTING_ENABLED=true
BI_DEFAULT_FORECAST_PERIODS=30
BI_MIN_DATA_POINTS=10
BI_CONFIDENCE_INTERVAL=0.95

# =============================================================================
# REAL-TIME DATA CONFIGURATION
# =============================================================================
# Update Intervals (in milliseconds)
WEATHER_UPDATE_INTERVAL=600000
TRAFFIC_UPDATE_INTERVAL=300000
SECURITY_UPDATE_INTERVAL=120000
MARKET_UPDATE_INTERVAL=900000
CRYPTO_UPDATE_INTERVAL=300000
SYSTEM_UPDATE_INTERVAL=30000

# API Timeout and Retry Configuration
API_TIMEOUT=5000
API_RETRY_ATTEMPTS=3
API_RETRY_DELAY=1000

# =============================================================================
# FEATURE FLAGS
# =============================================================================
# Enable/disable specific features
FEATURE_REAL_TIME_UPDATES=true
FEATURE_ADVANCED_ANALYTICS=true
FEATURE_FORECASTING=true
FEATURE_AUTOMATED_REPORTS=true
FEATURE_MOBILE_PUSH_NOTIFICATIONS=false
FEATURE_GEOFENCING=true
FEATURE_FACIAL_RECOGNITION=false
FEATURE_VOICE_COMMANDS=false

# =============================================================================
# THIRD-PARTY INTEGRATIONS
# =============================================================================
# Twilio (for SMS notifications)
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=+**********

# SendGrid (for email notifications)
SENDGRID_API_KEY=your_sendgrid_api_key
SENDGRID_FROM_EMAIL=<EMAIL>

# Firebase (for push notifications)
FIREBASE_PROJECT_ID=your_firebase_project_id
FIREBASE_PRIVATE_KEY_ID=your_firebase_private_key_id
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYOUR_PRIVATE_KEY\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_CLIENT_ID=your_firebase_client_id
FIREBASE_AUTH_URI=https://accounts.google.com/o/oauth2/auth
FIREBASE_TOKEN_URI=https://oauth2.googleapis.com/token

# =============================================================================
# BUSINESS LOGIC CONFIGURATION
# =============================================================================
# Business-specific configuration
DEFAULT_TIMEZONE=America/New_York
BUSINESS_HOURS_START=09:00
BUSINESS_HOURS_END=17:00
WEEKEND_SUPPORT=false
HOLIDAY_SUPPORT=false

# SLA Configuration
SLA_RESPONSE_TIME_CRITICAL=300
SLA_RESPONSE_TIME_HIGH=900
SLA_RESPONSE_TIME_MEDIUM=1800
SLA_RESPONSE_TIME_LOW=3600
