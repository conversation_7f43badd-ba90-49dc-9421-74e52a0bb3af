import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface ClientReport {
  id: string;
  title: string;
  type: 'incident' | 'patrol' | 'summary';
  date: string;
  status: 'new' | 'reviewed';
}

interface ClientReportsState {
  reports: ClientReport[];
  loading: boolean;
  error: string | null;
}

const initialState: ClientReportsState = {
  reports: [],
  loading: false,
  error: null,
};

const clientReportsSlice = createSlice({
  name: 'clientReports',
  initialState,
  reducers: {
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setReports: (state, action: PayloadAction<ClientReport[]>) => {
      state.reports = action.payload;
    },
    addReport: (state, action: PayloadAction<ClientReport>) => {
      state.reports.unshift(action.payload);
    },
    setError: (state, action: PayloadAction<string>) => {
      state.error = action.payload;
      state.loading = false;
    },
  },
});

export const { setLoading, setReports, addReport, setError } = clientReportsSlice.actions;
export default clientReportsSlice.reducer;
