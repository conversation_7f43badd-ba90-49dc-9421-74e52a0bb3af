import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface Site {
  id: string;
  name: string;
  address: string;
  status: 'active' | 'inactive';
  clientId: string;
  coordinates?: { lat: number; lng: number };
}

interface SitesState {
  sites: Site[];
  loading: boolean;
  error: string | null;
}

const initialState: SitesState = {
  sites: [],
  loading: false,
  error: null,
};

const sitesSlice = createSlice({
  name: 'adminSites',
  initialState,
  reducers: {
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setSites: (state, action: PayloadAction<Site[]>) => {
      state.sites = action.payload;
    },
    addSite: (state, action: PayloadAction<Site>) => {
      state.sites.push(action.payload);
    },
    updateSite: (state, action: PayloadAction<Site>) => {
      const index = state.sites.findIndex(site => site.id === action.payload.id);
      if (index !== -1) {
        state.sites[index] = action.payload;
      }
    },
    removeSite: (state, action: PayloadAction<string>) => {
      state.sites = state.sites.filter(site => site.id !== action.payload);
    },
    setError: (state, action: PayloadAction<string>) => {
      state.error = action.payload;
      state.loading = false;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
});

export const {
  setLoading,
  setSites,
  addSite,
  updateSite,
  removeSite,
  setError,
  clearError,
} = sitesSlice.actions;

export default sitesSlice.reducer;
