// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  clerkId   String   @unique
  email     String   @unique
  firstName String?
  lastName  String?
  role      UserRole @default(CLIENT)
  status    UserStatus @default(ACTIVE)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  clientProfile ClientProfile?
  adminProfile  AdminProfile?
  agentProfile  AgentProfile?
  
  // Activity tracking
  sessions      Session[]
  notifications Notification[]
  auditLogs     AuditLog[]

  @@map("users")
}

model ClientProfile {
  id       String @id @default(cuid())
  userId   String @unique
  user     User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  companyName    String?
  contactPerson  String?
  phone          String?
  address        String?
  city           String?
  country        String?
  
  // Business details
  industry       String?
  companySize    String?
  
  // Service details
  serviceLevel   ServiceLevel @default(STANDARD)
  contractStart  DateTime?
  contractEnd    DateTime?
  monthlyValue   Decimal?
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  sites         Site[]
  incidents     Incident[]
  reports       Report[]

  @@map("client_profiles")
}

model AdminProfile {
  id       String @id @default(cuid())
  userId   String @unique
  user     User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  department     String?
  position       String?
  permissions    String[] // JSON array of permissions
  accessLevel    AccessLevel @default(STANDARD)
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("admin_profiles")
}

model AgentProfile {
  id       String @id @default(cuid())
  userId   String @unique
  user     User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  employeeId     String   @unique
  phone          String?
  emergencyContact String?
  
  // Professional details
  hireDate       DateTime
  skills         String[] // JSON array of skills
  certifications String[] // JSON array of certifications
  
  // Current assignment
  currentSiteId  String?
  currentSite    Site?    @relation(fields: [currentSiteId], references: [id])
  
  // Performance
  rating         Float?
  completedShifts Int     @default(0)
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  shifts        Shift[]
  incidents     Incident[]
  reports       Report[]
  trackingLogs  TrackingLog[]

  @@map("agent_profiles")
}

model Site {
  id          String   @id @default(cuid())
  name        String
  address     String
  city        String
  country     String
  coordinates Json?    // {lat, lng}
  
  // Client relationship
  clientId    String
  client      ClientProfile @relation(fields: [clientId], references: [id])
  
  // Site details
  type        SiteType
  status      SiteStatus @default(ACTIVE)
  securityLevel SecurityLevel @default(MEDIUM)
  
  // Operational details
  maxAgents   Int      @default(1)
  shiftPattern String? // "24/7", "day_only", etc.
  equipment   String[] // JSON array of equipment
  
  // Emergency contacts
  emergencyContacts Json? // Array of contact objects
  
  // Contract details
  contractStart DateTime?
  contractEnd   DateTime?
  monthlyValue  Decimal?
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  agents       AgentProfile[]
  shifts       Shift[]
  incidents    Incident[]
  reports      Report[]
  trackingLogs TrackingLog[]

  @@map("sites")
}

model Shift {
  id        String     @id @default(cuid())
  agentId   String
  agent     AgentProfile @relation(fields: [agentId], references: [id])
  siteId    String
  site      Site       @relation(fields: [siteId], references: [id])
  
  startTime DateTime
  endTime   DateTime?
  status    ShiftStatus @default(SCHEDULED)
  
  // Shift details
  notes     String?
  checkIns  Json?      // Array of check-in objects
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  incidents Incident[]
  reports   Report[]

  @@map("shifts")
}

model Incident {
  id          String        @id @default(cuid())
  title       String
  description String
  type        IncidentType
  severity    IncidentSeverity @default(LOW)
  status      IncidentStatus @default(OPEN)
  
  // Location
  siteId      String
  site        Site          @relation(fields: [siteId], references: [id])
  location    String?       // Specific location within site
  
  // People involved
  reportedById String?
  reportedBy   AgentProfile? @relation(fields: [reportedById], references: [id])
  clientId     String?
  client       ClientProfile? @relation(fields: [clientId], references: [id])
  
  // Shift context
  shiftId     String?
  shift       Shift?        @relation(fields: [shiftId], references: [id])
  
  // Incident details
  occurredAt  DateTime
  resolvedAt  DateTime?
  evidence    Json?         // Array of evidence objects (photos, videos, etc.)
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  reports Report[]

  @@map("incidents")
}

model Report {
  id          String     @id @default(cuid())
  title       String
  content     String
  type        ReportType
  status      ReportStatus @default(DRAFT)
  
  // Author
  authorId    String
  author      AgentProfile @relation(fields: [authorId], references: [id])
  
  // Context
  siteId      String?
  site        Site?        @relation(fields: [siteId], references: [id])
  shiftId     String?
  shift       Shift?       @relation(fields: [shiftId], references: [id])
  incidentId  String?
  incident    Incident?    @relation(fields: [incidentId], references: [id])
  clientId    String?
  client      ClientProfile? @relation(fields: [clientId], references: [id])
  
  // Report details
  reportDate  DateTime
  attachments Json?        // Array of attachment objects
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("reports")
}

model TrackingLog {
  id        String   @id @default(cuid())
  agentId   String
  agent     AgentProfile @relation(fields: [agentId], references: [id])
  siteId    String?
  site      Site?    @relation(fields: [siteId], references: [id])
  
  // Location data
  latitude  Float
  longitude Float
  accuracy  Float?
  
  // Status
  status    String   // "on-duty", "off-duty", "break", "emergency"
  battery   Int?     // Battery percentage
  
  // Metadata
  deviceInfo Json?   // Device information
  
  timestamp DateTime @default(now())

  @@map("tracking_logs")
}

model Session {
  id        String   @id @default(cuid())
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  sessionToken String @unique
  deviceInfo   Json?
  ipAddress    String?
  userAgent    String?
  
  createdAt DateTime @default(now())
  expiresAt DateTime

  @@map("sessions")
}

model Notification {
  id        String   @id @default(cuid())
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  type      NotificationType
  priority  NotificationPriority @default(MEDIUM)
  title     String
  message   String
  
  // Delivery
  channels  String[] // Array of delivery channels
  status    NotificationStatus @default(PENDING)
  
  // Context
  relatedEntityType String?
  relatedEntityId   String?
  actionUrl         String?
  
  // Timing
  scheduledFor DateTime?
  sentAt       DateTime?
  readAt       DateTime?
  expiresAt    DateTime?
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("notifications")
}

model AuditLog {
  id        String   @id @default(cuid())
  userId    String?
  user      User?    @relation(fields: [userId], references: [id])
  
  action    String   // Action performed
  entity    String   // Entity type affected
  entityId  String?  // ID of affected entity
  
  // Context
  ipAddress String?
  userAgent String?
  metadata  Json?    // Additional context data
  
  timestamp DateTime @default(now())

  @@map("audit_logs")
}

model Integration {
  id          String   @id @default(cuid())
  name        String
  type        String   // "webhook", "api", "email", etc.
  
  // Configuration
  config      Json     // Integration-specific configuration
  credentials Json?    // Encrypted credentials
  
  // Status
  isActive    Boolean  @default(true)
  lastSync    DateTime?
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("integrations")
}

// Enums
enum UserRole {
  CLIENT
  ADMIN
  SUPERVISOR
  AGENT
}

enum UserStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
}

enum ServiceLevel {
  BASIC
  STANDARD
  PREMIUM
  ENTERPRISE
}

enum AccessLevel {
  STANDARD
  ELEVATED
  ADMIN
  SUPER_ADMIN
}

enum SiteType {
  BANK
  RETAIL
  OFFICE
  RESIDENTIAL
  INDUSTRIAL
  OTHER
}

enum SiteStatus {
  ACTIVE
  INACTIVE
  MAINTENANCE
}

enum SecurityLevel {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

enum ShiftStatus {
  SCHEDULED
  IN_PROGRESS
  COMPLETED
  CANCELLED
  NO_SHOW
}

enum IncidentType {
  SECURITY_BREACH
  THEFT
  VANDALISM
  MEDICAL_EMERGENCY
  FIRE
  TECHNICAL_ISSUE
  SUSPICIOUS_ACTIVITY
  ACCESS_VIOLATION
  OTHER
}

enum IncidentSeverity {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

enum IncidentStatus {
  OPEN
  IN_PROGRESS
  RESOLVED
  CLOSED
}

enum ReportType {
  DAILY
  INCIDENT
  SHIFT
  MAINTENANCE
  TRAINING
  CUSTOM
}

enum ReportStatus {
  DRAFT
  SUBMITTED
  REVIEWED
  APPROVED
  REJECTED
}

enum NotificationType {
  SYSTEM
  SECURITY
  INCIDENT
  SHIFT
  TRAINING
  MAINTENANCE
  BILLING
}

enum NotificationPriority {
  LOW
  MEDIUM
  HIGH
  URGENT
  CRITICAL
}

enum NotificationStatus {
  PENDING
  SENT
  DELIVERED
  READ
  FAILED
}
