import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface ClientAnalyticsData {
  period: string;
  siteMetrics: Record<string, number>;
  agentMetrics: Record<string, number>;
  incidentTrends: any[];
}

interface ClientAnalyticsState {
  data: ClientAnalyticsData | null;
  loading: boolean;
  error: string | null;
}

const initialState: ClientAnalyticsState = {
  data: null,
  loading: false,
  error: null,
};

const clientAnalyticsSlice = createSlice({
  name: 'clientAnalytics',
  initialState,
  reducers: {
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setData: (state, action: PayloadAction<ClientAnalyticsData>) => {
      state.data = action.payload;
    },
    setError: (state, action: PayloadAction<string>) => {
      state.error = action.payload;
      state.loading = false;
    },
  },
});

export const { setLoading, setData, setError } = clientAnalyticsSlice.actions;
export default clientAnalyticsSlice.reducer;
