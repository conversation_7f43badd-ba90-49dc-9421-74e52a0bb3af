import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth, useUser, SignUp } from '@clerk/clerk-react';
import {
  Box,
  Container,
  Typography,
  Card,
  CardContent,
  Button,
  AppBar,
  Toolbar,
  CircularProgress,
  Grid,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
} from '@mui/material';
import {
  Security as SecurityIcon,
  ArrowBack as ArrowBackIcon,
  PersonAdd as SignupIcon,
  CheckCircle as CheckIcon,
  Dashboard as DashboardIcon,
  Notifications as NotificationsIcon,
  Report as ReportIcon,
  Support as SupportIcon,
} from '@mui/icons-material';

const ClientSignupPage: React.FC = () => {
  const navigate = useNavigate();
  const { isSignedIn, isLoaded } = useAuth();
  const { user } = useUser();

  useEffect(() => {
    if (isLoaded && isSignedIn && user) {
      // User is signed up and signed in, redirect to client dashboard in unified app
      navigate('/client/dashboard');
    }
  }, [isLoaded, isSignedIn, user, navigate]);

  const handleBackToHome = () => {
    navigate('/');
  };

  const handleExistingClient = () => {
    navigate('/client/login');
  };

  if (!isLoaded) {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          minHeight: '100vh',
        }}
      >
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ minHeight: '100vh', backgroundColor: 'grey.50' }}>
      {/* Navigation Bar */}
      <AppBar position="static" elevation={0} sx={{ backgroundColor: 'primary.main' }}>
        <Toolbar>
          <Button
            color="inherit"
            startIcon={<ArrowBackIcon />}
            onClick={handleBackToHome}
            sx={{ mr: 2 }}
          >
            Back to Home
          </Button>
          <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1 }}>
            <SecurityIcon sx={{ mr: 2, fontSize: 32 }} />
            <Typography variant="h5" component="div" sx={{ fontWeight: 'bold' }}>
              BahinLink
            </Typography>
          </Box>
        </Toolbar>
      </AppBar>

      {/* Signup Content */}
      <Container maxWidth="lg" sx={{ py: 8 }}>
        <Grid container spacing={4}>
          <Grid item xs={12} md={6}>
            <Card elevation={3}>
              <CardContent sx={{ p: 4 }}>
                <Box sx={{ textAlign: 'center', mb: 4 }}>
                  <SignupIcon
                    sx={{
                      fontSize: 64,
                      color: 'primary.main',
                      mb: 2,
                    }}
                  />
                  <Typography variant="h4" gutterBottom sx={{ fontWeight: 'bold' }}>
                    Create Client Account
                  </Typography>
                  <Typography variant="subtitle1" color="text.secondary">
                    Join BahinLink to get real-time visibility into your security services
                  </Typography>
                </Box>

                {/* Clerk SignUp Component */}
                <Box sx={{ display: 'flex', justifyContent: 'center' }}>
                  <SignUp
                    routing="hash"
                    signInUrl="/client/login"
                    afterSignUpUrl="/client/dashboard"
                    appearance={{
                      elements: {
                        formButtonPrimary: {
                          backgroundColor: '#1976d2',
                          '&:hover': {
                            backgroundColor: '#1565c0',
                          },
                        },
                      },
                    }}
                  />
                </Box>

                <Box sx={{ mt: 4, textAlign: 'center' }}>
                  <Typography variant="body2" color="text.secondary">
                    Already have an account?{' '}
                    <Button
                      color="primary"
                      onClick={handleExistingClient}
                      sx={{ textTransform: 'none', textDecoration: 'underline', p: 0 }}
                    >
                      Sign in here
                    </Button>
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={6}>
            <Card elevation={3} sx={{ height: '100%' }}>
              <CardContent sx={{ p: 4 }}>
                <Typography variant="h5" gutterBottom sx={{ fontWeight: 'bold', mb: 3 }}>
                  What You'll Get
                </Typography>
                
                <List>
                  <ListItem>
                    <ListItemIcon>
                      <DashboardIcon color="primary" />
                    </ListItemIcon>
                    <ListItemText
                      primary="Real-time Dashboard"
                      secondary="Monitor your security personnel and site status in real-time"
                    />
                  </ListItem>
                  
                  <ListItem>
                    <ListItemIcon>
                      <ReportIcon color="primary" />
                    </ListItemIcon>
                    <ListItemText
                      primary="Detailed Reports"
                      secondary="Access patrol reports, incident reports, and performance analytics"
                    />
                  </ListItem>
                  
                  <ListItem>
                    <ListItemIcon>
                      <NotificationsIcon color="primary" />
                    </ListItemIcon>
                    <ListItemText
                      primary="Instant Notifications"
                      secondary="Get immediate alerts for incidents and important updates"
                    />
                  </ListItem>
                  
                  <ListItem>
                    <ListItemIcon>
                      <SupportIcon color="primary" />
                    </ListItemIcon>
                    <ListItemText
                      primary="Direct Communication"
                      secondary="Communicate directly with your security team and request services"
                    />
                  </ListItem>
                </List>

                <Box sx={{ mt: 4, p: 3, backgroundColor: 'success.light', borderRadius: 2 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <CheckIcon sx={{ color: 'success.dark', mr: 1 }} />
                    <Typography variant="h6" sx={{ color: 'success.dark', fontWeight: 'bold' }}>
                      Secure & Compliant
                    </Typography>
                  </Box>
                  <Typography variant="body2" sx={{ color: 'success.dark' }}>
                    Your data is protected with enterprise-grade security and GDPR compliance. 
                    All communications are encrypted and access is strictly controlled.
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Additional Information */}
        <Box sx={{ mt: 6, textAlign: 'center' }}>
          <Typography variant="h6" gutterBottom>
            Questions about our services?
          </Typography>
          <Typography variant="body1" color="text.secondary" paragraph>
            Contact our team to learn more about how BahinLink can enhance your security operations.
          </Typography>
          <Button
            variant="outlined"
            size="large"
            sx={{ px: 4 }}
          >
            Contact Sales
          </Button>
        </Box>
      </Container>
    </Box>
  );
};

export default ClientSignupPage;
