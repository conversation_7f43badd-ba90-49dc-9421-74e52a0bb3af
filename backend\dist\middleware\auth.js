"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.handleAuthError = exports.optionalAuth = exports.requireAgent = exports.requireClient = exports.requireAdmin = exports.requireRole = exports.requireAuth = void 0;
const clerk_sdk_node_1 = require("@clerk/clerk-sdk-node");
exports.requireAuth = (0, clerk_sdk_node_1.ClerkExpressRequireAuth)({
    onError: (error) => {
        console.error('Authentication error:', error);
        return {
            status: 401,
            message: 'Authentication required'
        };
    }
});
const requireRole = (allowedRoles) => {
    return (req, res, next) => {
        if (!req.auth) {
            return res.status(401).json({
                success: false,
                error: {
                    code: 'AUTHENTICATION_REQUIRED',
                    message: 'Authentication required'
                }
            });
        }
        const userRole = req.headers['x-user-role'] || 'CLIENT';
        if (!allowedRoles.includes(userRole)) {
            return res.status(403).json({
                success: false,
                error: {
                    code: 'INSUFFICIENT_PERMISSIONS',
                    message: 'Insufficient permissions to access this resource'
                }
            });
        }
        req.user = {
            id: req.auth.userId,
            role: userRole,
            email: req.auth.claims?.email || ''
        };
        next();
    };
};
exports.requireRole = requireRole;
exports.requireAdmin = (0, exports.requireRole)(['ADMIN', 'SUPERVISOR']);
exports.requireClient = (0, exports.requireRole)(['CLIENT']);
exports.requireAgent = (0, exports.requireRole)(['AGENT', 'SUPERVISOR', 'ADMIN']);
const optionalAuth = (req, res, next) => {
    if (process.env.NODE_ENV === 'development') {
        const mockUserId = req.headers['x-user-id'] || 'mock-user-id';
        const mockRole = req.headers['x-user-role'] || 'CLIENT';
        req.auth = {
            userId: mockUserId,
            sessionId: 'mock-session-id',
            claims: {
                email: '<EMAIL>'
            }
        };
        req.user = {
            id: mockUserId,
            role: mockRole,
            email: '<EMAIL>'
        };
    }
    next();
};
exports.optionalAuth = optionalAuth;
const handleAuthError = (error, req, res, next) => {
    if (error.name === 'UnauthorizedError' || error.status === 401) {
        return res.status(401).json({
            success: false,
            error: {
                code: 'AUTHENTICATION_FAILED',
                message: 'Invalid or expired authentication token'
            }
        });
    }
    next(error);
};
exports.handleAuthError = handleAuthError;
//# sourceMappingURL=auth.js.map