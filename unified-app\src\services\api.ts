import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';

// API Configuration
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000/api';

// Create axios instance
const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  async (config) => {
    try {
      // Get Clerk token if available
      const { getToken } = await import('@clerk/clerk-react');
      const token = await getToken();
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    } catch (error) {
      console.warn('Failed to get auth token:', error);
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized access
      window.location.href = '/';
    }
    return Promise.reject(error);
  }
);

// Admin API endpoints
export const adminAPI = {
  // Dashboard
  getDashboard: () => apiClient.get('/admin/dashboard'),
  
  // Users
  getUsers: (params?: any) => apiClient.get('/admin/users', { params }),
  getUser: (id: string) => apiClient.get(`/admin/users/${id}`),
  createUser: (data: any) => apiClient.post('/admin/users', data),
  updateUser: (id: string, data: any) => apiClient.put(`/admin/users/${id}`, data),
  deleteUser: (id: string) => apiClient.delete(`/admin/users/${id}`),
  
  // Sites
  getSites: () => apiClient.get('/admin/sites'),
  getSite: (id: string) => apiClient.get(`/admin/sites/${id}`),
  createSite: (data: any) => apiClient.post('/admin/sites', data),
  updateSite: (id: string, data: any) => apiClient.put(`/admin/sites/${id}`, data),
  deleteSite: (id: string) => apiClient.delete(`/admin/sites/${id}`),
  
  // Agents
  getAgents: () => apiClient.get('/admin/agents'),
  getAgent: (id: string) => apiClient.get(`/admin/agents/${id}`),
  updateAgent: (id: string, data: any) => apiClient.put(`/admin/agents/${id}`, data),
  
  // Shifts
  getShifts: (params?: any) => apiClient.get('/admin/shifts', { params }),
  createShift: (data: any) => apiClient.post('/admin/shifts', data),
  updateShift: (id: string, data: any) => apiClient.put(`/admin/shifts/${id}`, data),
  deleteShift: (id: string) => apiClient.delete(`/admin/shifts/${id}`),
  
  // Reports
  getReports: (params?: any) => apiClient.get('/admin/reports', { params }),
  getReport: (id: string) => apiClient.get(`/admin/reports/${id}`),
  
  // Analytics
  getAnalytics: (params?: any) => apiClient.get('/admin/analytics', { params }),
};

// Client API endpoints
export const clientAPI = {
  // Dashboard
  getDashboard: () => apiClient.get('/client-portal/dashboard'),
  
  // Sites
  getSites: () => apiClient.get('/client-portal/sites'),
  getSiteStatus: (id: string) => apiClient.get(`/client-portal/sites/${id}/status`),
  
  // Agents
  getAgents: () => apiClient.get('/client-portal/agents'),
  getAgentTracking: (siteId?: string) => 
    apiClient.get('/client-portal/tracking', { params: { siteId } }),
  
  // Reports
  getReports: (params?: any) => 
    apiClient.get('/client-portal/reports', { params }),
  getReport: (id: string) => 
    apiClient.get(`/client-portal/reports/${id}`),
  downloadReport: (id: string, format: 'pdf' | 'excel' | 'csv' = 'pdf') =>
    apiClient.get(`/client-portal/reports/${id}/download`, {
      params: { format },
      responseType: 'blob',
    }),
  
  // Service Requests
  getServiceRequests: () => apiClient.get('/client-portal/service-requests'),
  createServiceRequest: (data: any) => apiClient.post('/client-portal/service-requests', data),
  updateServiceRequest: (id: string, data: any) => 
    apiClient.put(`/client-portal/service-requests/${id}`, data),
  
  // Incidents
  getIncidents: () => apiClient.get('/client-portal/incidents'),
  getIncident: (id: string) => apiClient.get(`/client-portal/incidents/${id}`),
  
  // Analytics
  getAnalytics: (params?: any) => apiClient.get('/client-portal/analytics', { params }),
};

// Shared API endpoints
export const sharedAPI = {
  // Health check
  health: () => apiClient.get('/health'),
  
  // Notifications
  getNotifications: () => apiClient.get('/notifications'),
  markNotificationRead: (id: string) => apiClient.put(`/notifications/${id}/read`),
  markAllNotificationsRead: () => apiClient.put('/notifications/read-all'),
  
  // Profile
  getProfile: () => apiClient.get('/profile'),
  updateProfile: (data: any) => apiClient.put('/profile', data),
};

export default apiClient;
