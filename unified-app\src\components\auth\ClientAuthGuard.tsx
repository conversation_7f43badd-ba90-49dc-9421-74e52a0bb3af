import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth, useUser } from '@clerk/clerk-react';
import { Box, CircularProgress, Typography } from '@mui/material';

interface ClientAuthGuardProps {
  children: React.ReactNode;
}

const ClientAuthGuard: React.FC<ClientAuthGuardProps> = ({ children }) => {
  const { isSignedIn, isLoaded } = useAuth();
  const { user } = useUser();
  const location = useLocation();

  // Show loading while authentication is being checked
  if (!isLoaded) {
    return (
      <Box
        display="flex"
        flexDirection="column"
        alignItems="center"
        justifyContent="center"
        minHeight="100vh"
        gap={2}
      >
        <CircularProgress size={60} />
        <Typography variant="h6" color="textSecondary">
          Loading...
        </Typography>
      </Box>
    );
  }

  // Redirect to client login if not signed in
  if (!isSignedIn) {
    return <Navigate to="/client/login" state={{ from: location }} replace />;
  }

  // Check if user has client role (or default to client if no role specified)
  const userRole = user?.publicMetadata?.role as string || 'client';
  const isClient = userRole === 'client' || userRole === 'user';

  // Allow access for clients or users without specific roles
  if (!isClient && userRole !== 'admin' && userRole !== 'supervisor') {
    return <Navigate to="/" replace />;
  }

  // User is authenticated and has appropriate role
  return <>{children}</>;
};

export default ClientAuthGuard;
