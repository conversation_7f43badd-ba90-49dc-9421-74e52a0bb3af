import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth, useUser, SignIn } from '@clerk/clerk-react';
import {
  Box,
  Container,
  Typography,
  Card,
  CardContent,
  Button,
  AppBar,
  Toolbar,
  CircularProgress,
} from '@mui/material';
import {
  Security as SecurityIcon,
  ArrowBack as ArrowBackIcon,
  AdminPanelSettings as AdminIcon,
} from '@mui/icons-material';

const AdminLoginPage: React.FC = () => {
  console.log('AdminLoginPage component rendering...');
  const navigate = useNavigate();
  const { isSignedIn, isLoaded } = useAuth();
  const { user } = useUser();

  useEffect(() => {
    console.log('AdminLoginPage mounted - User state:', { isLoaded, isSignedIn, hasUser: !!user });

    // TEMPORARILY DISABLED: Only redirect if user is already authenticated and has proper role
    // This is disabled to test if the routing issue is caused by <PERSON> redirects
    /*
    if (isLoaded && isSignedIn && user) {
      console.log('Admin login page - User is already signed in:', {
        isLoaded,
        isSignedIn,
        userId: user.id,
        role: user.publicMetadata?.role
      });

      // Check if user has admin role
      const userRole = user.publicMetadata?.role;
      if (userRole === 'ADMIN' || userRole === 'SUPERVISOR' || userRole === 'admin' || userRole === 'supervisor') {
        console.log('User has admin role, redirecting to admin portal');
        const adminPortalUrl = process.env.REACT_APP_ADMIN_PORTAL_URL || 'http://localhost:3001';
        window.location.href = `${adminPortalUrl}/dashboard`;
      } else {
        console.warn('User does not have admin privileges, role:', userRole);
      }
    } else if (isLoaded && !isSignedIn) {
      console.log('User is not signed in, showing login form');
    } else if (!isLoaded) {
      console.log('Clerk is still loading...');
    }
    */
  }, [isLoaded, isSignedIn, user]);

  const handleBackToHome = () => {
    navigate('/');
  };

  if (!isLoaded) {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          minHeight: '100vh',
        }}
      >
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ minHeight: '100vh', backgroundColor: 'grey.50' }}>
      {/* Navigation Bar */}
      <AppBar position="static" elevation={0} sx={{ backgroundColor: 'primary.main' }}>
        <Toolbar>
          <Button
            color="inherit"
            startIcon={<ArrowBackIcon />}
            onClick={handleBackToHome}
            sx={{ mr: 2 }}
          >
            Back to Home
          </Button>
          <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1 }}>
            <SecurityIcon sx={{ mr: 2, fontSize: 32 }} />
            <Typography variant="h5" component="div" sx={{ fontWeight: 'bold' }}>
              BahinLink
            </Typography>
          </Box>
        </Toolbar>
      </AppBar>

      {/* Login Content */}
      <Container maxWidth="sm" sx={{ py: 8 }}>
        <Card elevation={3}>
          <CardContent sx={{ p: 4 }}>
            <Box sx={{ textAlign: 'center', mb: 4 }}>
              <AdminIcon
                sx={{
                  fontSize: 64,
                  color: 'primary.main',
                  mb: 2,
                }}
              />
              <Typography variant="h4" gutterBottom sx={{ fontWeight: 'bold' }}>
                Admin Portal
              </Typography>
              <Typography variant="subtitle1" color="text.secondary">
                Sign in to access the BahinLink administration dashboard
              </Typography>
            </Box>

            {/* Clerk SignIn Component */}
            <Box sx={{ display: 'flex', justifyContent: 'center' }}>
              <SignIn
                routing="hash"
                redirectUrl="/admin/login"
                appearance={{
                  elements: {
                    formButtonPrimary: {
                      backgroundColor: '#1976d2',
                      '&:hover': {
                        backgroundColor: '#1565c0',
                      },
                    },
                  },
                }}
              />
            </Box>

            <Box sx={{ mt: 4, p: 2, backgroundColor: 'info.light', borderRadius: 1 }}>
              <Typography variant="body2" color="info.contrastText" textAlign="center">
                <strong>Admin Access Only:</strong> This portal is restricted to authorized 
                administrators and supervisors. If you're a client, please use the 
                <Button
                  color="inherit"
                  onClick={() => navigate('/client/login')}
                  sx={{ textTransform: 'none', textDecoration: 'underline', p: 0, ml: 0.5 }}
                >
                  Client Portal
                </Button>
                .
              </Typography>
            </Box>
          </CardContent>
        </Card>

        {/* Additional Information */}
        <Box sx={{ mt: 4, textAlign: 'center' }}>
          <Typography variant="body2" color="text.secondary">
            Need help accessing your account?{' '}
            <Button
              color="primary"
              sx={{ textTransform: 'none', textDecoration: 'underline', p: 0 }}
            >
              Contact Support
            </Button>
          </Typography>
        </Box>
      </Container>
    </Box>
  );
};

export default AdminLoginPage;
