import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface ClientDashboardMetrics {
  activeSites: number;
  onDutyAgents: number;
  todayReports: number;
  openIncidents: number;
}

interface ClientDashboardState {
  metrics: ClientDashboardMetrics | null;
  recentActivity: Array<{
    id: string;
    type: string;
    message: string;
    timestamp: string;
  }>;
  loading: boolean;
  error: string | null;
}

const initialState: ClientDashboardState = {
  metrics: null,
  recentActivity: [],
  loading: false,
  error: null,
};

const clientDashboardSlice = createSlice({
  name: 'clientDashboard',
  initialState,
  reducers: {
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setMetrics: (state, action: PayloadAction<ClientDashboardMetrics>) => {
      state.metrics = action.payload;
    },
    setRecentActivity: (state, action: PayloadAction<ClientDashboardState['recentActivity']>) => {
      state.recentActivity = action.payload;
    },
    addActivity: (state, action: PayloadAction<ClientDashboardState['recentActivity'][0]>) => {
      state.recentActivity.unshift(action.payload);
      if (state.recentActivity.length > 20) {
        state.recentActivity = state.recentActivity.slice(0, 20);
      }
    },
    setError: (state, action: PayloadAction<string>) => {
      state.error = action.payload;
      state.loading = false;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
});

export const {
  setLoading,
  setMetrics,
  setRecentActivity,
  addActivity,
  setError,
  clearError,
} = clientDashboardSlice.actions;

export default clientDashboardSlice.reducer;
