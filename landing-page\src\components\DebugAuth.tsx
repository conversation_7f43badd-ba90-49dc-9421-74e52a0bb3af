import React from 'react';
import { useAuth, useUser } from '@clerk/clerk-react';
import { Box, Typography, Paper } from '@mui/material';

const DebugAuth: React.FC = () => {
  const { isSignedIn, isLoaded } = useAuth();
  const { user } = useUser();

  return (
    <Paper sx={{ p: 2, m: 2, backgroundColor: 'info.light' }}>
      <Typography variant="h6" gutterBottom>
        Debug: Authentication State
      </Typography>
      <Typography variant="body2">
        isLoaded: {isLoaded ? 'true' : 'false'}
      </Typography>
      <Typography variant="body2">
        isSignedIn: {isSignedIn ? 'true' : 'false'}
      </Typography>
      <Typography variant="body2">
        user: {user ? `${user.id} (${user.emailAddresses?.[0]?.emailAddress})` : 'null'}
      </Typography>
      <Typography variant="body2">
        role: {user?.publicMetadata?.role || 'none'}
      </Typography>
    </Paper>
  );
};

export default DebugAuth;
