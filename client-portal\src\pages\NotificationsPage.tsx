import React, { useState, useEffect, useCallback } from 'react';
import {
  <PERSON>,
  Grid,
  Card,
  CardContent,
  <PERSON>po<PERSON>,
  Button,
  Alert,
  CircularProgress,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Chip,
  IconButton,
  Badge,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Divider,
  Paper,
  Tooltip,
} from '@mui/material';
import {
  Notifications as NotificationsIcon,
  NotificationsActive as ActiveIcon,
  NotificationsOff as InactiveIcon,
  Refresh as RefreshIcon,
  MarkEmailRead as MarkReadIcon,
  Delete as DeleteIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  CheckCircle as SuccessIcon,
  Error as ErrorIcon,
  Security as SecurityIcon,
  Assignment as ReportIcon,
  Schedule as ScheduleIcon,
  AttachMoney as BillingIcon,
  Settings as SettingsIcon,
} from '@mui/icons-material';
import { useAuth as useClerkAuth } from '@clerk/clerk-react';
import { clientPortalAPI } from '../services/api';

interface Notification {
  id: string;
  type: 'SECURITY' | 'INCIDENT' | 'REPORT' | 'BILLING' | 'SCHEDULE' | 'SYSTEM' | 'MAINTENANCE';
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  title: string;
  message: string;
  isRead: boolean;
  createdAt: string;
  readAt?: string;
  actionUrl?: string;
  actionLabel?: string;
  metadata?: {
    siteId?: string;
    siteName?: string;
    agentId?: string;
    agentName?: string;
    incidentId?: string;
    reportId?: string;
    [key: string]: any;
  };
}

interface NotificationSettings {
  emailNotifications: boolean;
  pushNotifications: boolean;
  smsNotifications: boolean;
  securityAlerts: boolean;
  incidentReports: boolean;
  scheduleChanges: boolean;
  billingUpdates: boolean;
  systemMaintenance: boolean;
  reportDelivery: boolean;
}

const NotificationsPage: React.FC = () => {
  const { getToken } = useClerkAuth();
  
  // State management
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [settings, setSettings] = useState<NotificationSettings>({
    emailNotifications: true,
    pushNotifications: true,
    smsNotifications: false,
    securityAlerts: true,
    incidentReports: true,
    scheduleChanges: true,
    billingUpdates: true,
    systemMaintenance: true,
    reportDelivery: true,
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filterType, setFilterType] = useState<string>('all');
  const [filterRead, setFilterRead] = useState<string>('all');
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  // Data fetching functions
  const fetchNotifications = useCallback(async () => {
    try {
      setError(null);
      
      const token = await getToken();
      if (!token) {
        throw new Error('No authentication token available');
      }

      const response = await clientPortalAPI.getNotifications({
        type: filterType !== 'all' ? filterType : undefined,
        isRead: filterRead !== 'all' ? filterRead === 'read' : undefined,
      });
      
      setNotifications(response.data.notifications || []);
      setLastUpdated(new Date());
    } catch (err) {
      console.error('Error fetching notifications:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch notifications');
    } finally {
      setLoading(false);
    }
  }, [getToken, filterType, filterRead]);

  const markAsRead = async (notificationId: string) => {
    try {
      const token = await getToken();
      if (!token) {
        throw new Error('No authentication token available');
      }

      await clientPortalAPI.markNotificationRead(notificationId);
      
      setNotifications(prev =>
        prev.map(notification =>
          notification.id === notificationId
            ? { ...notification, isRead: true, readAt: new Date().toISOString() }
            : notification
        )
      );
    } catch (err) {
      console.error('Error marking notification as read:', err);
      setError(err instanceof Error ? err.message : 'Failed to mark notification as read');
    }
  };

  const markAllAsRead = async () => {
    try {
      const token = await getToken();
      if (!token) {
        throw new Error('No authentication token available');
      }

      await clientPortalAPI.markAllNotificationsRead();
      
      setNotifications(prev =>
        prev.map(notification => ({
          ...notification,
          isRead: true,
          readAt: new Date().toISOString(),
        }))
      );
    } catch (err) {
      console.error('Error marking all notifications as read:', err);
      setError(err instanceof Error ? err.message : 'Failed to mark all notifications as read');
    }
  };

  const updateSettings = async (newSettings: Partial<NotificationSettings>) => {
    try {
      const token = await getToken();
      if (!token) {
        throw new Error('No authentication token available');
      }

      const updatedSettings = { ...settings, ...newSettings };
      
      // In a real implementation, this would call an API endpoint
      // await clientPortalAPI.updateNotificationSettings(updatedSettings);
      
      setSettings(updatedSettings);
    } catch (err) {
      console.error('Error updating notification settings:', err);
      setError(err instanceof Error ? err.message : 'Failed to update notification settings');
    }
  };

  // Initial data load
  useEffect(() => {
    fetchNotifications();
  }, [fetchNotifications]);

  const handleRefresh = () => {
    fetchNotifications();
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'SECURITY': return <SecurityIcon />;
      case 'INCIDENT': return <WarningIcon />;
      case 'REPORT': return <ReportIcon />;
      case 'BILLING': return <BillingIcon />;
      case 'SCHEDULE': return <ScheduleIcon />;
      case 'SYSTEM': return <SettingsIcon />;
      default: return <InfoIcon />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'CRITICAL': return 'error';
      case 'HIGH': return 'warning';
      case 'MEDIUM': return 'info';
      default: return 'default';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'SECURITY': return '#d32f2f';
      case 'INCIDENT': return '#f57c00';
      case 'REPORT': return '#1976d2';
      case 'BILLING': return '#388e3c';
      case 'SCHEDULE': return '#7b1fa2';
      case 'SYSTEM': return '#616161';
      default: return '#1976d2';
    }
  };

  const unreadCount = notifications.filter(n => !n.isRead).length;

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Box display="flex" alignItems="center" gap={2}>
          <Typography variant="h4" component="h1">
            Notifications
          </Typography>
          <Badge badgeContent={unreadCount} color="primary">
            <NotificationsIcon />
          </Badge>
        </Box>
        <Box display="flex" gap={2}>
          <Button
            variant="outlined"
            startIcon={<MarkReadIcon />}
            onClick={markAllAsRead}
            disabled={unreadCount === 0}
          >
            Mark All Read
          </Button>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={handleRefresh}
            disabled={loading}
          >
            Refresh
          </Button>
        </Box>
      </Box>

      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Last Updated */}
      {lastUpdated && (
        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
          Last updated: {lastUpdated.toLocaleString()}
        </Typography>
      )}

      <Grid container spacing={3}>
        {/* Notifications List */}
        <Grid item xs={12} lg={8}>
          <Card>
            <CardContent>
              {/* Filters */}
              <Box display="flex" gap={2} mb={3}>
                <FormControl size="small" sx={{ minWidth: 150 }}>
                  <InputLabel>Filter by Type</InputLabel>
                  <Select
                    value={filterType}
                    label="Filter by Type"
                    onChange={(e) => setFilterType(e.target.value)}
                  >
                    <MenuItem value="all">All Types</MenuItem>
                    <MenuItem value="SECURITY">Security</MenuItem>
                    <MenuItem value="INCIDENT">Incidents</MenuItem>
                    <MenuItem value="REPORT">Reports</MenuItem>
                    <MenuItem value="BILLING">Billing</MenuItem>
                    <MenuItem value="SCHEDULE">Schedule</MenuItem>
                    <MenuItem value="SYSTEM">System</MenuItem>
                  </Select>
                </FormControl>
                <FormControl size="small" sx={{ minWidth: 150 }}>
                  <InputLabel>Filter by Status</InputLabel>
                  <Select
                    value={filterRead}
                    label="Filter by Status"
                    onChange={(e) => setFilterRead(e.target.value)}
                  >
                    <MenuItem value="all">All</MenuItem>
                    <MenuItem value="unread">Unread</MenuItem>
                    <MenuItem value="read">Read</MenuItem>
                  </Select>
                </FormControl>
              </Box>

              {/* Notifications */}
              <List>
                {notifications.map((notification, index) => (
                  <React.Fragment key={notification.id}>
                    <ListItem
                      sx={{
                        backgroundColor: notification.isRead ? 'transparent' : '#f3f4f6',
                        borderRadius: 1,
                        mb: 1,
                      }}
                    >
                      <ListItemAvatar>
                        <Avatar
                          sx={{
                            bgcolor: getTypeColor(notification.type),
                            width: 40,
                            height: 40,
                          }}
                        >
                          {getNotificationIcon(notification.type)}
                        </Avatar>
                      </ListItemAvatar>
                      <ListItemText
                        primary={
                          <Box display="flex" justifyContent="space-between" alignItems="center">
                            <Typography
                              variant="subtitle1"
                              sx={{
                                fontWeight: notification.isRead ? 400 : 600,
                                color: notification.isRead ? 'text.secondary' : 'text.primary',
                              }}
                            >
                              {notification.title}
                            </Typography>
                            <Box display="flex" alignItems="center" gap={1}>
                              <Chip
                                label={notification.priority}
                                size="small"
                                color={getPriorityColor(notification.priority) as any}
                              />
                              <Chip
                                label={notification.type}
                                size="small"
                                variant="outlined"
                              />
                              {!notification.isRead && (
                                <Tooltip title="Mark as read">
                                  <IconButton
                                    size="small"
                                    onClick={() => markAsRead(notification.id)}
                                  >
                                    <MarkReadIcon fontSize="small" />
                                  </IconButton>
                                </Tooltip>
                              )}
                            </Box>
                          </Box>
                        }
                        secondary={
                          <Box>
                            <Typography
                              variant="body2"
                              color="text.secondary"
                              sx={{ mb: 1 }}
                            >
                              {notification.message}
                            </Typography>
                            <Box display="flex" justifyContent="space-between" alignItems="center">
                              <Typography variant="caption" color="text.secondary">
                                {new Date(notification.createdAt).toLocaleString()}
                              </Typography>
                              {notification.actionUrl && notification.actionLabel && (
                                <Button
                                  size="small"
                                  variant="outlined"
                                  onClick={() => window.open(notification.actionUrl, '_blank')}
                                >
                                  {notification.actionLabel}
                                </Button>
                              )}
                            </Box>
                            {notification.metadata && (
                              <Box mt={1}>
                                {notification.metadata.siteName && (
                                  <Chip
                                    label={`Site: ${notification.metadata.siteName}`}
                                    size="small"
                                    variant="outlined"
                                    sx={{ mr: 1, mb: 0.5 }}
                                  />
                                )}
                                {notification.metadata.agentName && (
                                  <Chip
                                    label={`Agent: ${notification.metadata.agentName}`}
                                    size="small"
                                    variant="outlined"
                                    sx={{ mr: 1, mb: 0.5 }}
                                  />
                                )}
                              </Box>
                            )}
                          </Box>
                        }
                      />
                    </ListItem>
                    {index < notifications.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
                {notifications.length === 0 && (
                  <ListItem>
                    <ListItemText
                      primary="No notifications"
                      secondary="You're all caught up! New notifications will appear here."
                    />
                  </ListItem>
                )}
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Notification Settings */}
        <Grid item xs={12} lg={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Notification Settings
              </Typography>
              
              <Typography variant="subtitle2" sx={{ mt: 2, mb: 1 }}>
                Delivery Methods
              </Typography>
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.emailNotifications}
                    onChange={(e) => updateSettings({ emailNotifications: e.target.checked })}
                  />
                }
                label="Email Notifications"
              />
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.pushNotifications}
                    onChange={(e) => updateSettings({ pushNotifications: e.target.checked })}
                  />
                }
                label="Push Notifications"
              />
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.smsNotifications}
                    onChange={(e) => updateSettings({ smsNotifications: e.target.checked })}
                  />
                }
                label="SMS Notifications"
              />
              
              <Divider sx={{ my: 2 }} />
              
              <Typography variant="subtitle2" sx={{ mb: 1 }}>
                Notification Types
              </Typography>
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.securityAlerts}
                    onChange={(e) => updateSettings({ securityAlerts: e.target.checked })}
                  />
                }
                label="Security Alerts"
              />
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.incidentReports}
                    onChange={(e) => updateSettings({ incidentReports: e.target.checked })}
                  />
                }
                label="Incident Reports"
              />
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.scheduleChanges}
                    onChange={(e) => updateSettings({ scheduleChanges: e.target.checked })}
                  />
                }
                label="Schedule Changes"
              />
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.billingUpdates}
                    onChange={(e) => updateSettings({ billingUpdates: e.target.checked })}
                  />
                }
                label="Billing Updates"
              />
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.systemMaintenance}
                    onChange={(e) => updateSettings({ systemMaintenance: e.target.checked })}
                  />
                }
                label="System Maintenance"
              />
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.reportDelivery}
                    onChange={(e) => updateSettings({ reportDelivery: e.target.checked })}
                  />
                }
                label="Report Delivery"
              />
            </CardContent>
          </Card>

          {/* Quick Stats */}
          <Card sx={{ mt: 2 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Quick Stats
              </Typography>
              <Box display="flex" justifyContent="space-between" mb={1}>
                <Typography variant="body2">Total Notifications:</Typography>
                <Typography variant="body2" fontWeight={600}>
                  {notifications.length}
                </Typography>
              </Box>
              <Box display="flex" justifyContent="space-between" mb={1}>
                <Typography variant="body2">Unread:</Typography>
                <Typography variant="body2" fontWeight={600} color="primary">
                  {unreadCount}
                </Typography>
              </Box>
              <Box display="flex" justifyContent="space-between">
                <Typography variant="body2">Read:</Typography>
                <Typography variant="body2" fontWeight={600}>
                  {notifications.length - unreadCount}
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default NotificationsPage;