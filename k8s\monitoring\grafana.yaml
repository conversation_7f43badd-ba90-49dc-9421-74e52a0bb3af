apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-config
  namespace: bahinlink
data:
  grafana.ini: |
    [analytics]
    check_for_updates = true
    
    [grafana_net]
    url = https://grafana.net
    
    [log]
    mode = console
    level = info
    
    [paths]
    data = /var/lib/grafana/data
    logs = /var/log/grafana
    plugins = /var/lib/grafana/plugins
    provisioning = /etc/grafana/provisioning
    
    [server]
    protocol = http
    http_port = 3000
    domain = grafana.bahinlink.com
    enforce_domain = false
    root_url = %(protocol)s://%(domain)s:%(http_port)s/
    serve_from_sub_path = false
    
    [database]
    type = sqlite3
    path = grafana.db
    
    [session]
    provider = file
    
    [security]
    admin_user = admin
    admin_password = admin123
    secret_key = SW2YcwTIb9zpOOhoPsMm
    disable_gravatar = false
    
    [snapshots]
    external_enabled = true
    external_snapshot_url = https://snapshots-origin.raintank.io
    external_snapshot_name = Publish to snapshot.raintank.io
    
    [dashboards]
    versions_to_keep = 20
    
    [users]
    allow_sign_up = false
    allow_org_create = false
    auto_assign_org = true
    auto_assign_org_id = 1
    auto_assign_org_role = Viewer
    verify_email_enabled = false
    
    [auth]
    disable_login_form = false
    disable_signout_menu = false
    
    [auth.anonymous]
    enabled = false
    
    [smtp]
    enabled = true
    host = smtp.gmail.com:587
    user = <EMAIL>
    password = your-app-password
    from_address = <EMAIL>
    from_name = BahinLink Monitoring
    
    [alerting]
    enabled = true
    execute_alerts = true
    
    [metrics]
    enabled = true
    interval_seconds = 10

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-datasources
  namespace: bahinlink
data:
  datasources.yaml: |
    apiVersion: 1
    datasources:
    - name: Prometheus
      type: prometheus
      access: proxy
      url: http://prometheus:9090
      isDefault: true
      editable: true
    - name: Loki
      type: loki
      access: proxy
      url: http://loki:3100
      editable: true

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-dashboards-config
  namespace: bahinlink
data:
  dashboards.yaml: |
    apiVersion: 1
    providers:
    - name: 'default'
      orgId: 1
      folder: ''
      type: file
      disableDeletion: false
      updateIntervalSeconds: 10
      allowUiUpdates: true
      options:
        path: /var/lib/grafana/dashboards

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: bahinlink-dashboard
  namespace: bahinlink
data:
  bahinlink-overview.json: |
    {
      "dashboard": {
        "id": null,
        "title": "BahinLink System Overview",
        "tags": ["bahinlink"],
        "style": "dark",
        "timezone": "browser",
        "panels": [
          {
            "id": 1,
            "title": "API Response Time",
            "type": "stat",
            "targets": [
              {
                "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job=\"bahinlink-api\"}[5m]))",
                "legendFormat": "95th percentile"
              }
            ],
            "fieldConfig": {
              "defaults": {
                "unit": "s",
                "thresholds": {
                  "steps": [
                    {"color": "green", "value": null},
                    {"color": "yellow", "value": 0.5},
                    {"color": "red", "value": 1}
                  ]
                }
              }
            },
            "gridPos": {"h": 8, "w": 6, "x": 0, "y": 0}
          },
          {
            "id": 2,
            "title": "Request Rate",
            "type": "stat",
            "targets": [
              {
                "expr": "rate(http_requests_total{job=\"bahinlink-api\"}[5m])",
                "legendFormat": "Requests/sec"
              }
            ],
            "fieldConfig": {
              "defaults": {
                "unit": "reqps"
              }
            },
            "gridPos": {"h": 8, "w": 6, "x": 6, "y": 0}
          },
          {
            "id": 3,
            "title": "Error Rate",
            "type": "stat",
            "targets": [
              {
                "expr": "rate(http_requests_total{job=\"bahinlink-api\",status=~\"5..\"}[5m]) / rate(http_requests_total{job=\"bahinlink-api\"}[5m])",
                "legendFormat": "Error Rate"
              }
            ],
            "fieldConfig": {
              "defaults": {
                "unit": "percentunit",
                "thresholds": {
                  "steps": [
                    {"color": "green", "value": null},
                    {"color": "yellow", "value": 0.01},
                    {"color": "red", "value": 0.05}
                  ]
                }
              }
            },
            "gridPos": {"h": 8, "w": 6, "x": 12, "y": 0}
          },
          {
            "id": 4,
            "title": "Active Users",
            "type": "stat",
            "targets": [
              {
                "expr": "active_users_total",
                "legendFormat": "Active Users"
              }
            ],
            "gridPos": {"h": 8, "w": 6, "x": 18, "y": 0}
          },
          {
            "id": 5,
            "title": "API Response Time Over Time",
            "type": "timeseries",
            "targets": [
              {
                "expr": "histogram_quantile(0.50, rate(http_request_duration_seconds_bucket{job=\"bahinlink-api\"}[5m]))",
                "legendFormat": "50th percentile"
              },
              {
                "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job=\"bahinlink-api\"}[5m]))",
                "legendFormat": "95th percentile"
              },
              {
                "expr": "histogram_quantile(0.99, rate(http_request_duration_seconds_bucket{job=\"bahinlink-api\"}[5m]))",
                "legendFormat": "99th percentile"
              }
            ],
            "fieldConfig": {
              "defaults": {
                "unit": "s"
              }
            },
            "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}
          },
          {
            "id": 6,
            "title": "Memory Usage",
            "type": "timeseries",
            "targets": [
              {
                "expr": "container_memory_usage_bytes{pod=~\"api-.*\"} / 1024 / 1024",
                "legendFormat": "Memory Usage (MB)"
              }
            ],
            "fieldConfig": {
              "defaults": {
                "unit": "MB"
              }
            },
            "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}
          }
        ],
        "time": {
          "from": "now-1h",
          "to": "now"
        },
        "refresh": "5s"
      }
    }

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: grafana
  namespace: bahinlink
  labels:
    app: grafana
spec:
  replicas: 1
  selector:
    matchLabels:
      app: grafana
  template:
    metadata:
      labels:
        app: grafana
    spec:
      containers:
      - name: grafana
        image: grafana/grafana:10.0.0
        ports:
        - containerPort: 3000
        env:
        - name: GF_SECURITY_ADMIN_PASSWORD
          valueFrom:
            secretKeyRef:
              name: grafana-secret
              key: admin-password
        volumeMounts:
        - name: grafana-config
          mountPath: /etc/grafana/grafana.ini
          subPath: grafana.ini
        - name: grafana-datasources
          mountPath: /etc/grafana/provisioning/datasources
        - name: grafana-dashboards-config
          mountPath: /etc/grafana/provisioning/dashboards
        - name: grafana-dashboards
          mountPath: /var/lib/grafana/dashboards
        - name: grafana-storage
          mountPath: /var/lib/grafana
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "200m"
        livenessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: grafana-config
        configMap:
          name: grafana-config
      - name: grafana-datasources
        configMap:
          name: grafana-datasources
      - name: grafana-dashboards-config
        configMap:
          name: grafana-dashboards-config
      - name: grafana-dashboards
        configMap:
          name: bahinlink-dashboard
      - name: grafana-storage
        persistentVolumeClaim:
          claimName: grafana-storage

---
apiVersion: v1
kind: Service
metadata:
  name: grafana
  namespace: bahinlink
  labels:
    app: grafana
spec:
  selector:
    app: grafana
  ports:
  - name: web
    port: 3000
    targetPort: 3000
  type: ClusterIP

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: grafana-storage
  namespace: bahinlink
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: fast-ssd

---
apiVersion: v1
kind: Secret
metadata:
  name: grafana-secret
  namespace: bahinlink
type: Opaque
data:
  admin-password: YWRtaW4xMjM=  # admin123 base64 encoded
