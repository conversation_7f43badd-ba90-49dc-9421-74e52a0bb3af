import { Request, Response, NextFunction } from 'express';
import { ClerkExpressRequireAuth } from '@clerk/backend';

// Extend Express Request type to include auth
declare global {
  namespace Express {
    interface Request {
      auth?: {
        userId: string;
        sessionId: string;
        claims: any;
      };
      user?: {
        id: string;
        role: string;
        email: string;
      };
    }
  }
}

// Clerk authentication middleware
export const requireAuth = ClerkExpressRequireAuth({
  onError: (error) => {
    console.error('Authentication error:', error);
    return {
      status: 401,
      message: 'Authentication required'
    };
  }
});

// Role-based authorization middleware
export const requireRole = (allowedRoles: string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.auth) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'AUTHENTICATION_REQUIRED',
          message: 'Authentication required'
        }
      });
    }

    // In a real implementation, you would fetch the user's role from your database
    // For now, we'll use a mock role from the request or default to CLIENT
    const userRole = req.headers['x-user-role'] as string || 'CLIENT';

    if (!allowedRoles.includes(userRole)) {
      return res.status(403).json({
        success: false,
        error: {
          code: 'INSUFFICIENT_PERMISSIONS',
          message: 'Insufficient permissions to access this resource'
        }
      });
    }

    // Add user info to request
    req.user = {
      id: req.auth.userId,
      role: userRole,
      email: req.auth.claims?.email || ''
    };

    next();
  };
};

// Admin only middleware
export const requireAdmin = requireRole(['ADMIN', 'SUPERVISOR']);

// Client only middleware
export const requireClient = requireRole(['CLIENT']);

// Agent middleware (includes supervisors and admins)
export const requireAgent = requireRole(['AGENT', 'SUPERVISOR', 'ADMIN']);

// Optional authentication middleware (doesn't require auth but adds user info if present)
export const optionalAuth = (req: Request, res: Response, next: NextFunction) => {
  // For development, we'll skip Clerk validation and use mock data
  if (process.env.NODE_ENV === 'development') {
    const mockUserId = req.headers['x-user-id'] as string || 'mock-user-id';
    const mockRole = req.headers['x-user-role'] as string || 'CLIENT';
    
    req.auth = {
      userId: mockUserId,
      sessionId: 'mock-session-id',
      claims: {
        email: '<EMAIL>'
      }
    };

    req.user = {
      id: mockUserId,
      role: mockRole,
      email: '<EMAIL>'
    };
  }

  next();
};

// Error handling middleware for authentication errors
export const handleAuthError = (error: any, req: Request, res: Response, next: NextFunction) => {
  if (error.name === 'UnauthorizedError' || error.status === 401) {
    return res.status(401).json({
      success: false,
      error: {
        code: 'AUTHENTICATION_FAILED',
        message: 'Invalid or expired authentication token'
      }
    });
  }

  next(error);
};
