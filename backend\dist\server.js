"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.server = exports.app = void 0;
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const dotenv_1 = __importDefault(require("dotenv"));
const http_1 = require("http");
dotenv_1.default.config();
const app = (0, express_1.default)();
exports.app = app;
const server = (0, http_1.createServer)(app);
exports.server = server;
const PORT = process.env.PORT || 8000;
app.get('/test-route', (req, res) => {
    console.log('Test route hit!');
    res.json({ success: true, message: 'Test route is working!' });
});
const corsOptions = {
    origin: (origin, callback) => {
        if (!origin)
            return callback(null, true);
        const allowedOrigins = [
            'http://localhost:3001',
            'http://localhost:3000',
            'http://localhost:3002',
            'http://localhost:3003',
            process.env.CORS_ORIGIN
        ].filter(Boolean);
        if (allowedOrigins.includes(origin)) {
            callback(null, true);
        }
        else {
            console.warn(`Blocked request from unauthorized origin: ${origin}`);
            callback(new Error('Not allowed by CORS'));
        }
    },
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization'],
    credentials: true,
    optionsSuccessStatus: 200
};
app.use((0, cors_1.default)(corsOptions));
app.options('*', (0, cors_1.default)(corsOptions));
app.use(express_1.default.json({ limit: '10mb' }));
app.use(express_1.default.urlencoded({ extended: true, limit: '10mb' }));
app.get('/health', (req, res) => {
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        environment: process.env.NODE_ENV || 'development',
        version: '1.0.0'
    });
});
console.log('Registering API routes...');
app.get('/api/health', (req, res) => {
    res.json({
        status: 'healthy',
        services: {
            database: 'healthy',
            redis: 'healthy',
            websocket: 'healthy'
        },
        timestamp: new Date().toISOString()
    });
});
app.get('/api/users', (req, res) => {
    res.json({
        success: true,
        data: [
            { id: 1, name: 'Admin User', role: 'admin', email: '<EMAIL>' },
            { id: 2, name: 'Agent User', role: 'agent', email: '<EMAIL>' }
        ]
    });
});
app.get('/api/shifts', (req, res) => {
    res.json({
        success: true,
        data: [
            {
                id: 1,
                agentId: 2,
                siteId: 1,
                startTime: new Date().toISOString(),
                endTime: new Date(Date.now() + 8 * 60 * 60 * 1000).toISOString(),
                status: 'scheduled'
            }
        ]
    });
});
app.get('/api/sites', (req, res) => {
    res.json({
        success: true,
        data: [
            {
                id: 1,
                name: 'Downtown Office Complex',
                address: '123 Business St, City, State 12345',
                coordinates: { latitude: 40.7128, longitude: -74.0060 }
            }
        ]
    });
});
app.get('/api/reports', (req, res) => {
    res.json({
        success: true,
        data: [
            {
                id: 1,
                type: 'incident',
                title: 'Security Check',
                content: 'All clear during patrol',
                priority: 'normal',
                createdAt: new Date().toISOString()
            }
        ]
    });
});
console.log('Registering /api/analytics/dashboard route...');
app.get('/api/analytics/dashboard', (req, res) => {
    console.log('Received request to /api/analytics/dashboard');
    try {
        res.setHeader('Access-Control-Allow-Origin', req.headers.origin || '*');
        res.setHeader('Access-Control-Allow-Credentials', 'true');
        const dashboardData = {
            activeShifts: 5,
            totalAgents: 12,
            incidentsToday: 2,
            sitesMonitored: 8,
            timestamp: new Date().toISOString()
        };
        res.json({
            success: true,
            data: dashboardData
        });
    }
    catch (error) {
        console.error('Error in /api/analytics/dashboard:', error);
        res.status(500).json({
            success: false,
            error: {
                code: 'INTERNAL_SERVER_ERROR',
                message: 'Failed to fetch dashboard data'
            }
        });
    }
});
app.get('/api/client-portal/settings', (req, res) => {
    res.json({
        success: true,
        data: {
            id: '1',
            companyName: 'Sample Company',
            contactPerson: {
                name: 'John Doe',
                title: 'Security Manager',
                email: '<EMAIL>',
                phone: '******-0123'
            },
            billingAddress: {
                street: '123 Business St',
                city: 'Business City',
                state: 'BC',
                zipCode: '12345',
                country: 'USA'
            },
            serviceLevel: 'Premium',
            preferences: {
                timezone: 'America/New_York',
                dateFormat: 'MM/DD/YYYY',
                currency: 'USD',
                language: 'en',
                theme: 'light'
            },
            notifications: {
                emailNotifications: true,
                smsNotifications: false,
                pushNotifications: true,
                incidentAlerts: true,
                reportNotifications: true,
                billingReminders: true,
                maintenanceNotifications: false
            },
            security: {
                twoFactorEnabled: false,
                sessionTimeout: 30,
                ipWhitelist: [],
                apiAccess: true
            }
        }
    });
});
app.put('/api/client-portal/settings', (req, res) => {
    res.json({
        success: true,
        message: 'Settings updated successfully'
    });
});
app.get('/api/client-portal/emergency-contacts', (req, res) => {
    res.json({
        success: true,
        data: [
            {
                id: '1',
                name: 'Emergency Contact 1',
                title: 'Primary Contact',
                phone: '******-0199',
                email: '<EMAIL>',
                isPrimary: true
            }
        ]
    });
});
app.put('/api/client-portal/emergency-contacts', (req, res) => {
    res.json({
        success: true,
        message: 'Emergency contacts updated successfully'
    });
});
app.get('/api/client-portal/billing/payment-methods', (req, res) => {
    res.json({
        success: true,
        data: [
            {
                id: '1',
                type: 'CREDIT_CARD',
                isDefault: true,
                nickname: 'Business Visa',
                lastFour: '4242',
                expiryDate: '12/25',
                createdAt: new Date().toISOString()
            }
        ]
    });
});
app.get('/api/client-portal/billing/invoices', (req, res) => {
    res.json({
        success: true,
        data: [
            {
                id: '1',
                invoiceNumber: 'INV-2024-001',
                issueDate: new Date().toISOString(),
                dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
                amount: 1000,
                tax: 100,
                totalAmount: 1100,
                status: 'PAID',
                description: 'Monthly Security Services'
            }
        ]
    });
});
app.get('/api/client-portal/billing/subscription', (req, res) => {
    res.json({
        success: true,
        data: {
            id: '1',
            planName: 'Premium Security Plan',
            status: 'ACTIVE',
            billingCycle: 'MONTHLY',
            nextBillingDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
            amount: 1000,
            features: ['24/7 Monitoring', 'Incident Response', 'Monthly Reports']
        }
    });
});
app.get('/api/client-portal/billing/settings', (req, res) => {
    res.json({
        success: true,
        data: {
            autoPayEnabled: true,
            defaultPaymentMethodId: '1',
            billingNotifications: true,
            invoiceDelivery: 'EMAIL',
            paymentTerms: 30
        }
    });
});
app.get('/api/client-portal/dashboard', (req, res) => {
    res.json({
        success: true,
        data: {
            activeShifts: 3,
            totalAgents: 8,
            incidentsToday: 1,
            sitesMonitored: 5,
            recentActivity: [
                {
                    id: '1',
                    type: 'shift_start',
                    message: 'Agent John started shift at Downtown Office',
                    timestamp: new Date().toISOString()
                },
                {
                    id: '2',
                    type: 'incident',
                    message: 'Minor incident reported at Site A',
                    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()
                }
            ],
            siteStatus: [
                {
                    id: '1',
                    name: 'Downtown Office',
                    status: 'SECURE',
                    lastCheck: new Date().toISOString(),
                    agentOnDuty: 'John Doe'
                },
                {
                    id: '2',
                    name: 'Warehouse District',
                    status: 'SECURE',
                    lastCheck: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
                    agentOnDuty: 'Jane Smith'
                }
            ]
        }
    });
});
app.get('/api/client-portal/agent-tracking', (req, res) => {
    res.json({
        success: true,
        data: [
            {
                id: '1',
                name: 'John Doe',
                status: 'ON_DUTY',
                location: {
                    latitude: 40.7128,
                    longitude: -74.0060,
                    address: 'Downtown Office Complex'
                },
                lastUpdate: new Date().toISOString(),
                shiftStart: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
                checkpoints: [
                    {
                        id: '1',
                        name: 'Main Entrance',
                        timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
                        status: 'COMPLETED'
                    }
                ]
            }
        ]
    });
});
app.get('/api/client-portal/reports', (req, res) => {
    res.json({
        success: true,
        data: [
            {
                id: '1',
                type: 'INCIDENT',
                title: 'Security Incident Report',
                description: 'Minor security incident at main entrance',
                priority: 'MEDIUM',
                status: 'RESOLVED',
                createdAt: new Date().toISOString(),
                resolvedAt: new Date().toISOString(),
                agentName: 'John Doe',
                siteName: 'Downtown Office'
            }
        ]
    });
});
app.get('/api/client-portal/service-requests', (req, res) => {
    res.json({
        success: true,
        data: [
            {
                id: '1',
                type: 'MAINTENANCE',
                title: 'Camera Maintenance Request',
                description: 'Security camera needs cleaning',
                priority: 'LOW',
                status: 'PENDING',
                createdAt: new Date().toISOString(),
                requestedBy: 'John Doe'
            }
        ]
    });
});
app.post('/api/client-portal/service-requests', (req, res) => {
    res.json({
        success: true,
        message: 'Service request created successfully',
        data: {
            id: '2',
            ...req.body,
            createdAt: new Date().toISOString(),
            status: 'PENDING'
        }
    });
});
app.get('/api/client-portal/sites', (req, res) => {
    res.json({
        success: true,
        data: [
            {
                id: '1',
                name: 'Downtown Office Complex',
                address: '123 Business St, City, State 12345',
                coordinates: { latitude: 40.7128, longitude: -74.0060 },
                status: 'ACTIVE',
                agentOnDuty: 'John Doe',
                lastPatrol: new Date().toISOString()
            }
        ]
    });
});
app.get('/api/client-portal/analytics', (req, res) => {
    res.json({
        success: true,
        data: {
            incidentTrends: [
                { month: 'Jan', incidents: 5 },
                { month: 'Feb', incidents: 3 },
                { month: 'Mar', incidents: 7 }
            ],
            responseTime: {
                average: 8.5,
                target: 10
            },
            agentPerformance: [
                { name: 'John Doe', score: 95 },
                { name: 'Jane Smith', score: 92 }
            ]
        }
    });
});
app.get('/api/client-portal/communication/messages', (req, res) => {
    res.json({
        success: true,
        data: [
            {
                id: '1',
                from: 'Security Team',
                subject: 'Weekly Security Update',
                message: 'All systems operational this week.',
                timestamp: new Date().toISOString(),
                read: false
            }
        ]
    });
});
app.get('/api/client-portal/notifications', (req, res) => {
    res.json({
        success: true,
        data: [
            {
                id: '1',
                type: 'INFO',
                title: 'System Update',
                message: 'Security system updated successfully',
                timestamp: new Date().toISOString(),
                read: false
            }
        ]
    });
});
app.post('/api/client-portal/incidents', (req, res) => {
    res.json({
        success: true,
        message: 'Incident reported successfully',
        data: {
            id: '2',
            ...req.body,
            createdAt: new Date().toISOString(),
            status: 'REPORTED'
        }
    });
});
app.get('/api/client-portal/schedule-requests', (req, res) => {
    res.json({
        success: true,
        data: [
            {
                id: '1',
                type: 'ADDITIONAL_COVERAGE',
                description: 'Need extra security for event',
                requestedDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
                status: 'PENDING',
                createdAt: new Date().toISOString()
            }
        ]
    });
});
app.post('/api/client-portal/schedule-requests', (req, res) => {
    res.json({
        success: true,
        message: 'Schedule request created successfully',
        data: {
            id: '2',
            ...req.body,
            createdAt: new Date().toISOString(),
            status: 'PENDING'
        }
    });
});
app.post('/api/client-portal/feedback', (req, res) => {
    res.json({
        success: true,
        message: 'Feedback submitted successfully',
        data: {
            id: '1',
            ...req.body,
            createdAt: new Date().toISOString()
        }
    });
});
app.get('/api/auth/me', (req, res) => {
    res.json({
        success: true,
        data: {
            user: {
                id: 1,
                email: '<EMAIL>',
                username: 'admin',
                role: 'admin',
                permissions: ['users.read', 'users.write', 'shifts.read'],
                profile: {
                    firstName: 'Admin',
                    lastName: 'User',
                    avatar: null
                },
                lastLoginAt: new Date().toISOString(),
                isActive: true
            }
        }
    });
});
app.use((err, req, res, next) => {
    console.error('Error:', err);
    res.status(500).json({
        success: false,
        error: {
            code: 'INTERNAL_SERVER_ERROR',
            message: 'An internal server error occurred'
        }
    });
});
app.use('*', (req, res) => {
    res.status(404).json({
        success: false,
        error: {
            code: 'NOT_FOUND',
            message: 'Endpoint not found'
        }
    });
});
server.listen(PORT, () => {
    console.log(`🚀 BahinLink Backend API is running on port ${PORT}`);
    console.log(`📊 Health check: http://localhost:${PORT}/health`);
    console.log(`🔧 API endpoints: http://localhost:${PORT}/api`);
    console.log(`🌐 Environment: ${process.env.NODE_ENV || 'development'}`);
});
process.on('SIGTERM', () => {
    console.log('SIGTERM received, shutting down gracefully');
    server.close(() => {
        console.log('Server closed');
        process.exit(0);
    });
});
process.on('SIGINT', () => {
    console.log('SIGINT received, shutting down gracefully');
    server.close(() => {
        console.log('Server closed');
        process.exit(0);
    });
});
//# sourceMappingURL=server.js.map