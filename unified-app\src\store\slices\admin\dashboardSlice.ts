import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface DashboardMetrics {
  totalAgents: number;
  activeShifts: number;
  totalSites: number;
  todayIncidents: number;
  weeklyHours: number;
  monthlyRevenue: number;
}

interface AdminDashboardState {
  metrics: DashboardMetrics | null;
  recentActivities: Array<{
    id: string;
    type: string;
    message: string;
    timestamp: string;
    severity: 'low' | 'medium' | 'high';
  }>;
  loading: boolean;
  error: string | null;
}

const initialState: AdminDashboardState = {
  metrics: null,
  recentActivities: [],
  loading: false,
  error: null,
};

const adminDashboardSlice = createSlice({
  name: 'adminDashboard',
  initialState,
  reducers: {
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setMetrics: (state, action: PayloadAction<DashboardMetrics>) => {
      state.metrics = action.payload;
    },
    setRecentActivities: (state, action: PayloadAction<AdminDashboardState['recentActivities']>) => {
      state.recentActivities = action.payload;
    },
    addActivity: (state, action: PayloadAction<AdminDashboardState['recentActivities'][0]>) => {
      state.recentActivities.unshift(action.payload);
      // Keep only the latest 50 activities
      if (state.recentActivities.length > 50) {
        state.recentActivities = state.recentActivities.slice(0, 50);
      }
    },
    setError: (state, action: PayloadAction<string>) => {
      state.error = action.payload;
      state.loading = false;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
});

export const {
  setLoading,
  setMetrics,
  setRecentActivities,
  addActivity,
  setError,
  clearError,
} = adminDashboardSlice.actions;

export default adminDashboardSlice.reducer;
