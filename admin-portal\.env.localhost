# Admin Portal Environment Configuration for Localhost Nginx Setup
# Copy this to .env when using the unified localhost configuration

# API Configuration
REACT_APP_API_URL=http://localhost/api
REACT_APP_BASE_URL=http://localhost/admin

# Application Configuration
REACT_APP_NAME=BahinLink Admin Portal
REACT_APP_VERSION=1.0.0
REACT_APP_ENVIRONMENT=development

# Authentication Configuration
REACT_APP_AUTH_ENABLED=true
REACT_APP_SESSION_TIMEOUT=3600000

# Clerk Configuration (if using)
REACT_APP_CLERK_PUBLISHABLE_KEY=pk_test_YWxlcnQtY2F0ZmlzaC00Ny5jbGVyay5hY2NvdW50cy5kZXYk

# Feature Flags
REACT_APP_ENABLE_ANALYTICS=true
REACT_APP_ENABLE_REPORTS=true
REACT_APP_ENABLE_USER_MANAGEMENT=true
REACT_APP_ENABLE_SITE_MANAGEMENT=true
REACT_APP_ENABLE_SHIFT_MANAGEMENT=true

# UI Configuration
REACT_APP_THEME=light
REACT_APP_SIDEBAR_COLLAPSED=false
REACT_APP_ENABLE_DARK_MODE=true

# Monitoring Configuration
REACT_APP_ENABLE_ERROR_TRACKING=true
REACT_APP_ENABLE_PERFORMANCE_MONITORING=true

# WebSocket Configuration
REACT_APP_WEBSOCKET_URL=http://localhost/socket.io
REACT_APP_ENABLE_REALTIME=true

# File Upload Configuration
REACT_APP_MAX_FILE_SIZE=10485760
REACT_APP_ALLOWED_FILE_TYPES=.pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png

# Pagination Configuration
REACT_APP_DEFAULT_PAGE_SIZE=20
REACT_APP_MAX_PAGE_SIZE=100

# Development Configuration
REACT_APP_DEBUG=true
REACT_APP_MOCK_API=false

# Build Configuration
GENERATE_SOURCEMAP=true
REACT_APP_BUILD_PATH=/admin