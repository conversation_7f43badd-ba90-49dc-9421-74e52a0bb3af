import express from 'express';
import { PrismaClient } from '@prisma/client';
import { body, query, param, validationResult } from 'express-validator';

const router = express.Router();
const prisma = new PrismaClient();

// Middleware to handle validation errors
const handleValidationErrors = (req: express.Request, res: express.Response, next: express.NextFunction) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: {
        code: 'VALIDATION_ERROR',
        message: 'Invalid input data',
        details: errors.array()
      }
    });
  }
  next();
};

// GET /api/client-portal/dashboard - Get client dashboard data
router.get('/dashboard', async (req, res) => {
  try {
    // For now, we'll use a mock client ID. In production, this would come from the authenticated user
    const clientId = 'mock-client-id'; // This should be extracted from the authenticated user

    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    // Get client's sites
    const sites = await prisma.site.findMany({
      where: {
        clientId: clientId,
        deletedAt: null
      },
      select: {
        id: true,
        name: true,
        status: true
      }
    });

    const siteIds = sites.map(site => site.id);

    // Get active shifts for client's sites
    const activeShifts = await prisma.shift.count({
      where: {
        siteId: { in: siteIds },
        status: 'IN_PROGRESS',
        deletedAt: null
      }
    });

    // Get today's incidents
    const incidentsToday = await prisma.report.count({
      where: {
        siteId: { in: siteIds },
        type: 'INCIDENT',
        createdAt: {
          gte: today,
          lt: tomorrow
        },
        deletedAt: null
      }
    });

    // Get pending service requests
    const pendingRequests = await prisma.clientRequest.count({
      where: {
        clientId: clientId,
        status: {
          in: ['OPEN', 'ASSIGNED', 'IN_PROGRESS']
        },
        deletedAt: null
      }
    });

    // Get recent reports
    const recentReports = await prisma.report.findMany({
      where: {
        siteId: { in: siteIds },
        deletedAt: null
      },
      include: {
        agent: {
          select: {
            user: {
              select: {
                firstName: true,
                lastName: true
              }
            }
          }
        },
        site: {
          select: {
            name: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 5
    });

    res.json({
      success: true,
      data: {
        overview: {
          activeSites: sites.filter(site => site.status === 'ACTIVE').length,
          activeShifts,
          incidentsToday,
          pendingRequests
        },
        recentReports: recentReports.map(report => ({
          id: report.id,
          type: report.type,
          title: report.title,
          priority: report.priority,
          status: report.status,
          agentName: `${report.agent.user.firstName} ${report.agent.user.lastName}`,
          siteName: report.site.name,
          createdAt: report.createdAt
        }))
      }
    });
  } catch (error) {
    console.error('Error fetching client dashboard:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to fetch dashboard data'
      }
    });
  }
});

// GET /api/client-portal/reports - Get client's reports
router.get('/reports', [
  query('page').optional().isInt({ min: 1 }).toInt(),
  query('limit').optional().isInt({ min: 1, max: 100 }).toInt(),
  query('type').optional().isIn(['PATROL', 'INCIDENT', 'INSPECTION', 'MAINTENANCE', 'EMERGENCY']),
  query('priority').optional().isIn(['LOW', 'NORMAL', 'HIGH', 'CRITICAL']),
  query('siteId').optional().isUUID(),
  query('startDate').optional().isISO8601(),
  query('endDate').optional().isISO8601(),
], handleValidationErrors, async (req, res) => {
  try {
    const clientId = 'mock-client-id'; // Extract from authenticated user
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const type = req.query.type as string;
    const priority = req.query.priority as string;
    const siteId = req.query.siteId as string;
    const startDate = req.query.startDate as string;
    const endDate = req.query.endDate as string;
    const offset = (page - 1) * limit;

    // Get client's sites
    const clientSites = await prisma.site.findMany({
      where: {
        clientId: clientId,
        deletedAt: null
      },
      select: { id: true }
    });

    const siteIds = clientSites.map(site => site.id);

    // Build where clause
    const where: any = {
      siteId: { in: siteIds },
      deletedAt: null,
      status: {
        in: ['APPROVED', 'ARCHIVED'] // Only show approved/archived reports to clients
      }
    };

    if (type) where.type = type;
    if (priority) where.priority = priority;
    if (siteId && siteIds.includes(siteId)) where.siteId = siteId;

    if (startDate || endDate) {
      where.createdAt = {};
      if (startDate) where.createdAt.gte = new Date(startDate);
      if (endDate) where.createdAt.lte = new Date(endDate);
    }

    // Get reports with pagination
    const [reports, total] = await Promise.all([
      prisma.report.findMany({
        where,
        skip: offset,
        take: limit,
        include: {
          agent: {
            select: {
              user: {
                select: {
                  firstName: true,
                  lastName: true
                }
              }
            }
          },
          site: {
            select: {
              name: true,
              address: true
            }
          },
          mediaFiles: {
            select: {
              id: true,
              filename: true,
              fileType: true
            }
          }
        },
        orderBy: { createdAt: 'desc' }
      }),
      prisma.report.count({ where })
    ]);

    res.json({
      success: true,
      data: {
        reports,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error('Error fetching client reports:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to fetch reports'
      }
    });
  }
});

// GET /api/client-portal/service-requests - Get client's service requests
router.get('/service-requests', [
  query('page').optional().isInt({ min: 1 }).toInt(),
  query('limit').optional().isInt({ min: 1, max: 100 }).toInt(),
  query('status').optional().isIn(['OPEN', 'ASSIGNED', 'IN_PROGRESS', 'RESOLVED', 'CLOSED', 'CANCELLED']),
  query('type').optional().isIn(['ADDITIONAL_PATROL', 'EMERGENCY_RESPONSE', 'MAINTENANCE', 'CONSULTATION', 'OTHER']),
], handleValidationErrors, async (req, res) => {
  try {
    const clientId = 'mock-client-id'; // Extract from authenticated user
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const status = req.query.status as string;
    const type = req.query.type as string;
    const offset = (page - 1) * limit;

    // Build where clause
    const where: any = {
      clientId: clientId,
      deletedAt: null
    };

    if (status) where.status = status;
    if (type) where.type = type;

    // Get service requests with pagination
    const [requests, total] = await Promise.all([
      prisma.clientRequest.findMany({
        where,
        skip: offset,
        take: limit,
        include: {
          site: {
            select: {
              name: true,
              address: true
            }
          },
          assignee: {
            select: {
              firstName: true,
              lastName: true
            }
          }
        },
        orderBy: { createdAt: 'desc' }
      }),
      prisma.clientRequest.count({ where })
    ]);

    res.json({
      success: true,
      data: {
        requests,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error('Error fetching service requests:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to fetch service requests'
      }
    });
  }
});

// POST /api/client-portal/service-requests - Create new service request
router.post('/service-requests', [
  body('type').isIn(['ADDITIONAL_PATROL', 'EMERGENCY_RESPONSE', 'MAINTENANCE', 'CONSULTATION', 'OTHER']),
  body('title').isString().isLength({ min: 1, max: 200 }).trim(),
  body('description').isString().isLength({ min: 1, max: 2000 }).trim(),
  body('priority').optional().isIn(['LOW', 'MEDIUM', 'HIGH', 'URGENT']),
  body('siteId').optional().isUUID(),
  body('urgentContact').optional().isObject(),
], handleValidationErrors, async (req, res) => {
  try {
    const clientId = 'mock-client-id'; // Extract from authenticated user
    const { type, title, description, priority = 'MEDIUM', siteId, urgentContact } = req.body;

    // Validate site belongs to client if provided
    if (siteId) {
      const site = await prisma.site.findFirst({
        where: {
          id: siteId,
          clientId: clientId,
          deletedAt: null
        }
      });

      if (!site) {
        return res.status(404).json({
          success: false,
          error: {
            code: 'SITE_NOT_FOUND',
            message: 'Site not found or does not belong to client'
          }
        });
      }
    }

    // Create service request
    const request = await prisma.clientRequest.create({
      data: {
        clientId,
        type,
        title,
        description,
        priority,
        siteId,
        urgentContact,
        status: 'OPEN'
      },
      include: {
        site: {
          select: {
            name: true,
            address: true
          }
        }
      }
    });

    res.status(201).json({
      success: true,
      data: { request },
      message: 'Service request created successfully'
    });
  } catch (error) {
    console.error('Error creating service request:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to create service request'
      }
    });
  }
});

// GET /api/client-portal/sites - Get client's sites
router.get('/sites', async (req, res) => {
  try {
    const clientId = 'mock-client-id'; // Extract from authenticated user

    const sites = await prisma.site.findMany({
      where: {
        clientId: clientId,
        deletedAt: null
      },
      include: {
        shifts: {
          where: {
            status: 'IN_PROGRESS',
            deletedAt: null
          },
          include: {
            agent: {
              select: {
                user: {
                  select: {
                    firstName: true,
                    lastName: true
                  }
                }
              }
            }
          }
        },
        _count: {
          select: {
            reports: {
              where: {
                deletedAt: null,
                createdAt: {
                  gte: new Date(new Date().setDate(new Date().getDate() - 7))
                }
              }
            }
          }
        }
      },
      orderBy: { name: 'asc' }
    });

    res.json({
      success: true,
      data: { sites }
    });
  } catch (error) {
    console.error('Error fetching client sites:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to fetch sites'
      }
    });
  }
});

export default router;
