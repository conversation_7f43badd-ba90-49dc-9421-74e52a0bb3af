import { configureStore } from '@reduxjs/toolkit';
import { combineReducers } from '@reduxjs/toolkit';

// Shared slices
import authReducer from './slices/shared/authSlice';
import uiReducer from './slices/shared/uiSlice';
import notificationsReducer from './slices/shared/notificationsSlice';

// Admin-specific slices
import adminDashboardReducer from './slices/admin/dashboardSlice';
import adminUsersReducer from './slices/admin/usersSlice';
import adminSitesReducer from './slices/admin/sitesSlice';
import adminAgentsReducer from './slices/admin/agentsSlice';
import adminShiftsReducer from './slices/admin/shiftsSlice';
import adminReportsReducer from './slices/admin/reportsSlice';
import adminAnalyticsReducer from './slices/admin/analyticsSlice';

// Client-specific slices
import clientDashboardReducer from './slices/client/dashboardSlice';
import clientSitesReducer from './slices/client/sitesSlice';
import clientAgentsReducer from './slices/client/agentsSlice';
import clientReportsReducer from './slices/client/reportsSlice';
import clientAnalyticsReducer from './slices/client/analyticsSlice';

const rootReducer = combineReducers({
  // Shared state
  auth: authReducer,
  ui: uiReducer,
  notifications: notificationsReducer,
  
  // Admin state
  admin: combineReducers({
    dashboard: adminDashboardReducer,
    users: adminUsersReducer,
    sites: adminSitesReducer,
    agents: adminAgentsReducer,
    shifts: adminShiftsReducer,
    reports: adminReportsReducer,
    analytics: adminAnalyticsReducer,
  }),
  
  // Client state
  client: combineReducers({
    dashboard: clientDashboardReducer,
    sites: clientSitesReducer,
    agents: clientAgentsReducer,
    reports: clientReportsReducer,
    analytics: clientAnalyticsReducer,
  }),
});

export const store = configureStore({
  reducer: rootReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [
          'persist/PERSIST',
          'persist/REHYDRATE',
          'auth/setClerkUser',
          'auth/setClerkSession',
        ],
        ignoredActionsPaths: [
          'payload.primaryEmailAddress',
          'payload.primaryPhoneNumber',
          'payload.createdAt',
          'payload.lastSignInAt',
          'payload.expireAt',
        ],
      },
    }),
  devTools: process.env.NODE_ENV !== 'production',
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

// Typed hooks
export { useAppDispatch, useAppSelector } from './hooks';
