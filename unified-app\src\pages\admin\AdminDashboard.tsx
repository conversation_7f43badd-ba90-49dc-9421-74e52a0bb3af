import React, { useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  CircularProgress,
  Alert,
} from '@mui/material';
import {
  People as PeopleIcon,
  Security as SecurityIcon,
  LocationOn as LocationIcon,
  Warning as WarningIcon,
  TrendingUp as TrendingUpIcon,
  AttachMoney as MoneyIcon,
} from '@mui/icons-material';

import { useAppSelector, useAppDispatch } from '../../store/hooks';
import { setMetrics, setLoading, setError } from '../../store/slices/admin/dashboardSlice';

const AdminDashboard: React.FC = () => {
  const dispatch = useAppDispatch();
  const { metrics, loading, error } = useAppSelector(state => state.admin.dashboard);

  useEffect(() => {
    // Simulate loading dashboard data
    dispatch(setLoading(true));
    
    // Mock data - in real app, this would be an API call
    setTimeout(() => {
      dispatch(setMetrics({
        totalAgents: 45,
        activeShifts: 12,
        totalSites: 8,
        todayIncidents: 3,
        weeklyHours: 1240,
        monthlyRevenue: 125000,
      }));
      dispatch(setLoading(false));
    }, 1000);
  }, [dispatch]);

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress size={60} />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 3 }}>
        {error}
      </Alert>
    );
  }

  const metricCards = [
    {
      title: 'Total Agents',
      value: metrics?.totalAgents || 0,
      icon: <PeopleIcon sx={{ fontSize: 40 }} />,
      color: 'primary.main',
      bgColor: 'primary.light',
    },
    {
      title: 'Active Shifts',
      value: metrics?.activeShifts || 0,
      icon: <SecurityIcon sx={{ fontSize: 40 }} />,
      color: 'success.main',
      bgColor: 'success.light',
    },
    {
      title: 'Total Sites',
      value: metrics?.totalSites || 0,
      icon: <LocationIcon sx={{ fontSize: 40 }} />,
      color: 'info.main',
      bgColor: 'info.light',
    },
    {
      title: 'Today\'s Incidents',
      value: metrics?.todayIncidents || 0,
      icon: <WarningIcon sx={{ fontSize: 40 }} />,
      color: 'warning.main',
      bgColor: 'warning.light',
    },
    {
      title: 'Weekly Hours',
      value: metrics?.weeklyHours || 0,
      icon: <TrendingUpIcon sx={{ fontSize: 40 }} />,
      color: 'secondary.main',
      bgColor: 'secondary.light',
    },
    {
      title: 'Monthly Revenue',
      value: `$${(metrics?.monthlyRevenue || 0).toLocaleString()}`,
      icon: <MoneyIcon sx={{ fontSize: 40 }} />,
      color: 'success.main',
      bgColor: 'success.light',
    },
  ];

  return (
    <Box>
      <Typography variant="h4" gutterBottom sx={{ fontWeight: 'bold', mb: 4 }}>
        Admin Dashboard
      </Typography>

      <Grid container spacing={3}>
        {metricCards.map((card, index) => (
          <Grid item xs={12} sm={6} md={4} key={index}>
            <Card elevation={2} sx={{ height: '100%' }}>
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography variant="h6" color="text.secondary" gutterBottom>
                      {card.title}
                    </Typography>
                    <Typography variant="h4" sx={{ fontWeight: 'bold', color: card.color }}>
                      {card.value}
                    </Typography>
                  </Box>
                  <Box
                    sx={{
                      p: 2,
                      borderRadius: 2,
                      backgroundColor: card.bgColor,
                      color: card.color,
                    }}
                  >
                    {card.icon}
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      <Grid container spacing={3} sx={{ mt: 2 }}>
        <Grid item xs={12} md={8}>
          <Card elevation={2}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Recent Activity
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Real-time activity feed will be displayed here
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={4}>
          <Card elevation={2}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Quick Actions
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Quick action buttons will be displayed here
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default AdminDashboard;
