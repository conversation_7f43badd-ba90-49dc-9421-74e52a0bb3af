# Database Configuration
DATABASE_URL="prisma+postgres://accelerate.prisma-data.net/?api_key=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhcGlfa2V5IjoiMDFKWTJGRFNRODNSRzQySjNYUVNRUjVZMjIiLCJ0ZW5hbnRfaWQiOiIyYzRiNmMzOWU0YmI3YTYzMDIwMzMwZGI0N2NhNDliMzgzMTY2OTM4MGYzNjIyMjEwM2JiM2M5MmRkZjE3M2Q4IiwiaW50ZXJuYWxfc2VjcmV0IjoiZDAwZTI4YjMtMThjOS00MGI5LThlYTAtYTg4YjEzNmM2ZGJiIn0.nmjxODOf4RnJVbd-Y5q41dyMyEfpk7MGNh7xulQTu9o"
DATABASE_POOL_SIZE=20
DATABASE_TIMEOUT=30000

# Redis Configuration
REDIS_URL="redis://localhost:6379"
REDIS_TTL=3600

# Server Configuration
NODE_ENV=development
PORT=3000
API_VERSION=v1
CORS_ORIGIN=http://localhost:3001

# Clerk Authentication Integration (Backend)
CLERK_SECRET_KEY=sk_test_your-clerk-secret-key-here
CLERK_WEBHOOK_SECRET=whsec_your-clerk-webhook-secret-here
CLERK_PUBLISHABLE_KEY=pk_test_YWxlcnQtY2F0ZmlzaC00Ny5jbGVyay5hY2NvdW50cy5kZXYk

# Encryption (for general data encryption, not authentication)
ENCRYPTION_KEY=your-32-byte-encryption-key-here

# File Storage (AWS S3)
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_S3_BUCKET=bahinlink-media
AWS_S3_REGION=us-east-1

# External APIs
GOOGLE_MAPS_API_KEY=your-google-maps-api-key
GOOGLE_GEOCODING_API_KEY=your-google-geocoding-api-key

# Push Notifications (Firebase)
FIREBASE_PROJECT_ID=your-firebase-project-id
FIREBASE_CLIENT_EMAIL=your-firebase-client-email
FIREBASE_PRIVATE_KEY=your-firebase-private-key
FCM_SERVER_KEY=your-firebase-server-key
FCM_SENDER_ID=your-firebase-sender-id

# SMS/Email Services
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_PHONE_NUMBER=+**********

SENDGRID_API_KEY=your-sendgrid-api-key
SENDGRID_FROM_EMAIL=<EMAIL>
SENDGRID_FROM_NAME=BahinLink

# Logging
LOG_LEVEL=info
LOG_FILE=logs/app.log

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000
AUTH_RATE_LIMIT_MAX=5

# Security
HELMET_CSP_ENABLED=true
CORS_CREDENTIALS=true
TRUST_PROXY=false

# Monitoring
SENTRY_DSN=your-sentry-dsn
NEW_RELIC_LICENSE_KEY=your-new-relic-key

# Development/Testing
MOCK_EXTERNAL_SERVICES=false
ENABLE_API_DOCS=true
ENABLE_PRISMA_STUDIO=false

# Geofencing
DEFAULT_GEOFENCE_RADIUS=100
MAX_GEOFENCE_RADIUS=1000

# File Upload
MAX_FILE_SIZE=********
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,video/mp4,video/quicktime,application/pdf

# Session Management (handled by Clerk)
# SESSION_TIMEOUT - Managed by Clerk
# REFRESH_TOKEN_ROTATION - Managed by Clerk
# DEVICE_TRUST_DURATION - Managed by Clerk

# Background Jobs
CRON_CLEANUP_ENABLED=true
CRON_BACKUP_ENABLED=true
CRON_NOTIFICATIONS_ENABLED=true

# Feature Flags
FEATURE_2FA_ENABLED=true
FEATURE_OFFLINE_SYNC=true
FEATURE_REAL_TIME_TRACKING=true
FEATURE_CLIENT_PORTAL=true

# Notification Services Configuration
# Twilio SMS Configuration
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=+**********

# Email Configuration (SMTP)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
SMTP_FROM=BahinLink Security <<EMAIL>>

# Firebase Push Notifications
FIREBASE_SERVICE_ACCOUNT_KEY={"type":"service_account","project_id":"your_project_id"}

# AWS S3 File Storage
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1
AWS_S3_BUCKET=bahinlink-files
