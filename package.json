{"name": "bahinlink-system", "version": "1.0.0", "description": "BahinLink Security Workforce Management System - Unified application with landing page, admin portal, client portal, and backend API", "main": "backend/src/server.ts", "type": "commonjs", "scripts": {"start": "node scripts/start.js", "dev": "npm run setup && concurrently --kill-others --names \"BACKEND,LANDING,ADMIN,CLIENT\" --prefix-colors \"blue,green,yellow,magenta\" \"npm run dev:backend\" \"npm run dev:landing\" \"npm run dev:admin\" \"npm run dev:client\"", "setup": "npm run install:all && npm run db:setup", "install:all": "npm run install:backend && npm run install:landing && npm run install:admin && npm run install:client", "install:backend": "cd backend && npm install --silent", "install:landing": "cd landing-page && npm install --silent", "install:admin": "cd admin-portal && npm install --silent", "install:client": "cd client-portal && npm install --silent", "start:backend": "cd backend && npm start", "start:landing": "cd landing-page && npm start", "start:admin": "cd admin-portal && npm start", "start:client": "cd client-portal && npm start", "dev:backend": "cd backend && npm run dev", "dev:landing": "cd landing-page && npm start", "dev:admin": "cd admin-portal && npm start", "dev:client": "cd client-portal && npm start", "build": "npm run build:all", "build:all": "npm run build:backend && npm run build:landing && npm run build:admin && npm run build:client", "build:backend": "cd backend && npm run build", "build:landing": "cd landing-page && npm run build", "build:admin": "cd admin-portal && npm run build", "build:client": "cd client-portal && npm run build", "test": "npm run test:backend && npm run test:admin && npm run test:client", "test:backend": "cd backend && npm test", "test:admin": "cd admin-portal && npm test", "test:client": "cd client-portal && npm test", "test:e2e": "npm run test:backend && npm run test:admin && npm run test:client", "lint": "npm run lint:backend && npm run lint:admin && npm run lint:client", "lint:backend": "cd backend && npm run lint", "lint:admin": "cd admin-portal && npm run lint", "lint:client": "cd client-portal && npm run lint", "db:setup": "npm run db:generate && npm run db:migrate", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev", "db:migrate:prod": "prisma migrate deploy", "db:seed": "node prisma/seed.js", "db:studio": "prisma studio", "db:reset": "prisma migrate reset", "db:push": "prisma db push", "db:pull": "prisma db pull", "health": "node scripts/health-check.js", "docker:build": "docker build -t bahinlink-system .", "docker:compose": "docker-compose up -d", "docker:compose:down": "docker-compose down", "clean": "npm run clean:all", "clean:all": "npm run clean:backend && npm run clean:landing && npm run clean:admin && npm run clean:client", "clean:backend": "cd backend && npm run clean", "clean:landing": "cd landing-page && rm -rf node_modules build", "clean:admin": "cd admin-portal && rm -rf node_modules build", "clean:client": "cd client-portal && rm -rf node_modules build", "postinstall": "npm run db:generate"}, "keywords": ["security-management", "workforce-management", "real-time-tracking", "incident-management", "client-portal", "admin-dashboard", "authentication", "react", "nodejs", "typescript", "prisma", "postgresql", "clerk-auth", "material-ui"], "author": "BahinLink Development Team", "license": "MIT", "dependencies": {"@clerk/backend": "^2.4.0", "@prisma/client": "^5.7.1", "aws-sdk": "^2.1692.0", "axios": "^1.6.2", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.3.1", "exceljs": "^4.4.0", "exif-parser": "^0.1.12", "express": "^4.18.2", "express-async-errors": "^3.1.1", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "ffprobe": "^1.1.2", "ffprobe-static": "^3.1.0", "firebase-admin": "^13.4.0", "fluent-ffmpeg": "^2.1.3", "geolib": "^3.3.4", "helmet": "^7.1.0", "ioredis": "^5.3.2", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "moment": "^2.29.4", "moment-timezone": "^0.5.43", "morgan": "^1.10.0", "multer": "^2.0.1", "node-cache": "^5.1.2", "node-cron": "^3.0.3", "nodemailer": "^6.10.1", "pdf-parse": "^1.1.1", "pdfkit": "^0.17.1", "qrcode": "^1.5.4", "redis": "^5.5.6", "sharp": "^0.34.2", "simple-statistics": "^7.8.3", "socket.io": "^4.7.4", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "twilio": "^5.7.2", "uuid": "^11.1.0", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1"}, "devDependencies": {"@babel/cli": "^7.23.4", "@babel/core": "^7.23.6", "@babel/preset-env": "^7.23.6", "@types/jest": "^29.5.8", "concurrently": "^8.2.2", "eslint": "^8.55.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-jest": "^27.6.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-security": "^1.7.1", "husky": "^8.0.3", "jest": "^29.7.0", "lint-staged": "^15.2.0", "nodemon": "^3.0.2", "prettier": "^3.1.1", "prisma": "^5.7.1", "rimraf": "^5.0.5", "snyk": "^1.1266.0", "supertest": "^6.3.3", "wait-on": "^7.2.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-org/agent-system.git"}, "bugs": {"url": "https://github.com/your-org/agent-system/issues"}, "homepage": "https://github.com/your-org/agent-system#readme", "prisma": {"seed": "node prisma/seed.js"}, "jest": {"testEnvironment": "node", "collectCoverageFrom": ["src/**/*.js", "!src/server.js", "!src/config/**"], "coverageThreshold": {"global": {"branches": 80, "functions": 80, "lines": 80, "statements": 80}}, "testMatch": ["**/tests/**/*.test.js", "**/tests/**/*.spec.js"], "setupFilesAfterEnv": ["<rootDir>/tests/setup.js"]}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,css,md}": ["prettier --write"]}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "npm run test:ci"}}}