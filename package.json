{"name": "agent-system", "version": "1.0.0", "description": "Production-ready agent management system with real-time data integration, advanced analytics, and comprehensive monitoring", "main": "src/server.js", "type": "module", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "build": "npm run build:clean && npm run build:compile", "build:clean": "<PERSON><PERSON><PERSON> dist", "build:compile": "babel src --out-dir dist --copy-files", "test": "npm run test:unit && npm run test:integration", "test:unit": "jest --testPathPattern=tests/unit", "test:integration": "jest --testPathPattern=tests/integration", "test:e2e": "jest --testPathPattern=tests/e2e", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "lint": "eslint src tests --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint src tests --ext .js,.jsx,.ts,.tsx --fix", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{js,jsx,ts,tsx,json,css,md}\"", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev", "db:migrate:prod": "prisma migrate deploy", "db:seed": "node prisma/seed.js", "db:studio": "prisma studio", "db:reset": "prisma migrate reset", "db:push": "prisma db push", "db:pull": "prisma db pull", "health": "node scripts/health-check.js", "security:audit": "npm audit && npm run security:snyk", "security:snyk": "snyk test", "security:fix": "npm audit fix", "docker:build": "docker build -t agent-system .", "docker:run": "docker run -p 3003:3003 --env-file .env agent-system", "docker:compose": "docker-compose up -d", "docker:compose:down": "docker-compose down", "logs": "tail -f logs/app.log", "logs:error": "tail -f logs/error.log", "monitor": "node scripts/monitor.js", "backup": "node scripts/backup.js", "restore": "node scripts/restore.js", "clean": "rimraf node_modules dist logs/*.log", "precommit": "npm run lint && npm run test:unit", "prepush": "npm run test && npm run security:audit", "postinstall": "npm run db:generate"}, "keywords": ["agent-management", "real-time-data", "business-intelligence", "monitoring", "analytics", "security", "performance", "production-ready", "nodejs", "prisma", "redis", "postgresql"], "author": "Agent System Development Team", "license": "MIT", "dependencies": {"@clerk/backend": "^2.4.0", "@prisma/client": "^5.7.1", "aws-sdk": "^2.1692.0", "axios": "^1.6.2", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.3.1", "exceljs": "^4.4.0", "exif-parser": "^0.1.12", "express": "^4.18.2", "express-async-errors": "^3.1.1", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "ffprobe": "^1.1.2", "ffprobe-static": "^3.1.0", "firebase-admin": "^13.4.0", "fluent-ffmpeg": "^2.1.3", "geolib": "^3.3.4", "helmet": "^7.1.0", "ioredis": "^5.3.2", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "moment": "^2.29.4", "moment-timezone": "^0.5.43", "morgan": "^1.10.0", "multer": "^2.0.1", "node-cache": "^5.1.2", "node-cron": "^3.0.3", "nodemailer": "^6.10.1", "pdf-parse": "^1.1.1", "pdfkit": "^0.17.1", "qrcode": "^1.5.4", "redis": "^5.5.6", "sharp": "^0.34.2", "simple-statistics": "^7.8.3", "socket.io": "^4.7.4", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "twilio": "^5.7.2", "uuid": "^11.1.0", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1"}, "devDependencies": {"@babel/cli": "^7.23.4", "@babel/core": "^7.23.6", "@babel/preset-env": "^7.23.6", "@types/jest": "^29.5.8", "eslint": "^8.55.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-jest": "^27.6.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-security": "^1.7.1", "husky": "^8.0.3", "jest": "^29.7.0", "lint-staged": "^15.2.0", "nodemon": "^3.0.2", "prettier": "^3.1.1", "prisma": "^5.7.1", "rimraf": "^5.0.5", "snyk": "^1.1266.0", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-org/agent-system.git"}, "bugs": {"url": "https://github.com/your-org/agent-system/issues"}, "homepage": "https://github.com/your-org/agent-system#readme", "prisma": {"seed": "node prisma/seed.js"}, "jest": {"testEnvironment": "node", "collectCoverageFrom": ["src/**/*.js", "!src/server.js", "!src/config/**"], "coverageThreshold": {"global": {"branches": 80, "functions": 80, "lines": 80, "statements": 80}}, "testMatch": ["**/tests/**/*.test.js", "**/tests/**/*.spec.js"], "setupFilesAfterEnv": ["<rootDir>/tests/setup.js"]}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,css,md}": ["prettier --write"]}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "npm run test:ci"}}}