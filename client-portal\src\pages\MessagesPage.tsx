import React, { useState, useEffect, useCallback } from 'react';
import {
  <PERSON>,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Alert,
  CircularProgress,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Chip,
  Divider,
  Paper,
  IconButton,
  Badge,
} from '@mui/material';
import {
  Message as MessageIcon,
  Send as SendIcon,
  Add as AddIcon,
  Refresh as RefreshIcon,
  Reply as ReplyIcon,
  Person as PersonIcon,
  Schedule as ScheduleIcon,
} from '@mui/icons-material';
import { useAuth as useClerkAuth } from '@clerk/clerk-react';
import { clientPortalAPI } from '../services/api';

interface Message {
  id: string;
  conversationId: string;
  senderId: string;
  senderName: string;
  senderType: 'CLIENT' | 'AGENT' | 'ADMIN' | 'SYSTEM';
  content: string;
  timestamp: string;
  isRead: boolean;
  attachments?: Attachment[];
}

interface Attachment {
  id: string;
  filename: string;
  url: string;
  size: number;
  type: string;
}

interface Conversation {
  id: string;
  subject: string;
  participants: Participant[];
  lastMessage: Message;
  unreadCount: number;
  status: 'ACTIVE' | 'CLOSED' | 'ARCHIVED';
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  createdAt: string;
  updatedAt: string;
}

interface Participant {
  id: string;
  name: string;
  type: 'CLIENT' | 'AGENT' | 'ADMIN';
  avatar?: string;
}

const MessagesPage: React.FC = () => {
  const { getToken } = useClerkAuth();
  
  // State management
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [selectedConversation, setSelectedConversation] = useState<Conversation | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [newMessageDialogOpen, setNewMessageDialogOpen] = useState(false);
  const [newMessage, setNewMessage] = useState('');
  const [newSubject, setNewSubject] = useState('');
  const [replyMessage, setReplyMessage] = useState('');
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  // Data fetching functions
  const fetchConversations = useCallback(async () => {
    try {
      setError(null);
      
      const token = await getToken();
      if (!token) {
        throw new Error('No authentication token available');
      }

      const response = await clientPortalAPI.getMessages();
      setConversations(response.data.conversations || []);
      setLastUpdated(new Date());
    } catch (err) {
      console.error('Error fetching conversations:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch conversations');
    } finally {
      setLoading(false);
    }
  }, [getToken]);

  const fetchMessages = useCallback(async (conversationId: string) => {
    try {
      const token = await getToken();
      if (!token) {
        throw new Error('No authentication token available');
      }

      const response = await clientPortalAPI.getConversation(conversationId);
      setMessages(response.data.messages || []);
      
      // Mark messages as read
      const unreadMessages = response.data.messages.filter((msg: Message) => !msg.isRead);
      if (unreadMessages.length > 0) {
        // Update conversation unread count
        setConversations(prev => 
          prev.map(conv => 
            conv.id === conversationId 
              ? { ...conv, unreadCount: 0 }
              : conv
          )
        );
      }
    } catch (err) {
      console.error('Error fetching messages:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch messages');
    }
  }, [getToken]);

  const sendMessage = async (content: string, conversationId?: string) => {
    try {
      const token = await getToken();
      if (!token) {
        throw new Error('No authentication token available');
      }

      const messageData = {
        content,
        conversationId,
        subject: conversationId ? undefined : newSubject,
      };

      const response = await clientPortalAPI.sendMessage(messageData);
      
      if (conversationId) {
        // Add to existing conversation
        setMessages(prev => [...prev, response.data.message]);
      } else {
        // New conversation created
        await fetchConversations();
        setSelectedConversation(response.data.conversation);
        setMessages([response.data.message]);
        setNewMessageDialogOpen(false);
        setNewSubject('');
      }
      
      setNewMessage('');
      setReplyMessage('');
    } catch (err) {
      console.error('Error sending message:', err);
      setError(err instanceof Error ? err.message : 'Failed to send message');
    }
  };

  // Initial data load
  useEffect(() => {
    fetchConversations();
  }, [fetchConversations]);

  const handleConversationSelect = (conversation: Conversation) => {
    setSelectedConversation(conversation);
    fetchMessages(conversation.id);
  };

  const handleRefresh = () => {
    fetchConversations();
    if (selectedConversation) {
      fetchMessages(selectedConversation.id);
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'URGENT': return 'error';
      case 'HIGH': return 'warning';
      case 'MEDIUM': return 'info';
      default: return 'default';
    }
  };

  const getSenderTypeColor = (type: string) => {
    switch (type) {
      case 'AGENT': return '#1976d2';
      case 'ADMIN': return '#d32f2f';
      case 'SYSTEM': return '#9e9e9e';
      default: return '#4caf50';
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1">
          Messages
        </Typography>
        <Box display="flex" gap={2}>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => setNewMessageDialogOpen(true)}
          >
            New Message
          </Button>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={handleRefresh}
            disabled={loading}
          >
            Refresh
          </Button>
        </Box>
      </Box>

      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Last Updated */}
      {lastUpdated && (
        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
          Last updated: {lastUpdated.toLocaleString()}
        </Typography>
      )}

      <Grid container spacing={3} sx={{ height: 'calc(100vh - 250px)' }}>
        {/* Conversations List */}
        <Grid item xs={12} md={4}>
          <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
            <CardContent sx={{ pb: 1 }}>
              <Typography variant="h6" gutterBottom>
                Conversations ({conversations.length})
              </Typography>
            </CardContent>
            <Box sx={{ flexGrow: 1, overflow: 'auto' }}>
              <List sx={{ pt: 0 }}>
                {conversations.map((conversation) => (
                  <React.Fragment key={conversation.id}>
                    <ListItem
                      button
                      selected={selectedConversation?.id === conversation.id}
                      onClick={() => handleConversationSelect(conversation)}
                      sx={{
                        backgroundColor: selectedConversation?.id === conversation.id ? '#e3f2fd' : 'transparent',
                        '&:hover': { backgroundColor: '#f5f5f5' },
                      }}
                    >
                      <ListItemAvatar>
                        <Badge badgeContent={conversation.unreadCount} color="primary">
                          <Avatar sx={{ bgcolor: getSenderTypeColor('CLIENT') }}>
                            <MessageIcon />
                          </Avatar>
                        </Badge>
                      </ListItemAvatar>
                      <ListItemText
                        primary={
                          <Box display="flex" justifyContent="space-between" alignItems="center">
                            <Typography variant="subtitle2" noWrap>
                              {conversation.subject}
                            </Typography>
                            <Chip
                              label={conversation.priority}
                              size="small"
                              color={getPriorityColor(conversation.priority) as any}
                            />
                          </Box>
                        }
                        secondary={
                          <Box>
                            <Typography variant="body2" color="text.secondary" noWrap>
                              {conversation.lastMessage.content}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {new Date(conversation.lastMessage.timestamp).toLocaleDateString()}
                            </Typography>
                          </Box>
                        }
                      />
                    </ListItem>
                    <Divider />
                  </React.Fragment>
                ))}
                {conversations.length === 0 && (
                  <ListItem>
                    <ListItemText
                      primary="No conversations"
                      secondary="Start a new conversation to get help from our team"
                    />
                  </ListItem>
                )}
              </List>
            </Box>
          </Card>
        </Grid>

        {/* Messages */}
        <Grid item xs={12} md={8}>
          <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
            {selectedConversation ? (
              <>
                <CardContent sx={{ pb: 1, borderBottom: '1px solid #e0e0e0' }}>
                  <Box display="flex" justifyContent="space-between" alignItems="center">
                    <Typography variant="h6">
                      {selectedConversation.subject}
                    </Typography>
                    <Chip
                      label={selectedConversation.status}
                      size="small"
                      color={selectedConversation.status === 'ACTIVE' ? 'success' : 'default'}
                    />
                  </Box>
                  <Typography variant="body2" color="text.secondary">
                    {selectedConversation.participants.length} participants
                  </Typography>
                </CardContent>
                
                {/* Messages List */}
                <Box sx={{ flexGrow: 1, overflow: 'auto', p: 2 }}>
                  {messages.map((message) => (
                    <Paper
                      key={message.id}
                      sx={{
                        p: 2,
                        mb: 2,
                        backgroundColor: message.senderType === 'CLIENT' ? '#e3f2fd' : '#f5f5f5',
                        marginLeft: message.senderType === 'CLIENT' ? 'auto' : 0,
                        marginRight: message.senderType === 'CLIENT' ? 0 : 'auto',
                        maxWidth: '70%',
                      }}
                    >
                      <Box display="flex" alignItems="center" mb={1}>
                        <Avatar
                          sx={{
                            width: 24,
                            height: 24,
                            mr: 1,
                            bgcolor: getSenderTypeColor(message.senderType),
                            fontSize: '0.75rem',
                          }}
                        >
                          {message.senderName.charAt(0)}
                        </Avatar>
                        <Typography variant="subtitle2">
                          {message.senderName}
                        </Typography>
                        <Typography variant="caption" color="text.secondary" sx={{ ml: 'auto' }}>
                          {new Date(message.timestamp).toLocaleString()}
                        </Typography>
                      </Box>
                      <Typography variant="body2">
                        {message.content}
                      </Typography>
                      {message.attachments && message.attachments.length > 0 && (
                        <Box mt={1}>
                          {message.attachments.map((attachment) => (
                            <Chip
                              key={attachment.id}
                              label={attachment.filename}
                              size="small"
                              onClick={() => window.open(attachment.url, '_blank')}
                              sx={{ mr: 1, mb: 1 }}
                            />
                          ))}
                        </Box>
                      )}
                    </Paper>
                  ))}
                </Box>
                
                {/* Reply Box */}
                <Box sx={{ p: 2, borderTop: '1px solid #e0e0e0' }}>
                  <Box display="flex" gap={1}>
                    <TextField
                      fullWidth
                      multiline
                      rows={2}
                      placeholder="Type your reply..."
                      value={replyMessage}
                      onChange={(e) => setReplyMessage(e.target.value)}
                      variant="outlined"
                      size="small"
                    />
                    <IconButton
                      color="primary"
                      onClick={() => sendMessage(replyMessage, selectedConversation.id)}
                      disabled={!replyMessage.trim()}
                    >
                      <SendIcon />
                    </IconButton>
                  </Box>
                </Box>
              </>
            ) : (
              <Box
                display="flex"
                flexDirection="column"
                justifyContent="center"
                alignItems="center"
                height="100%"
              >
                <MessageIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                <Typography variant="h6" color="text.secondary">
                  Select a conversation to view messages
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Choose from the list on the left or start a new conversation
                </Typography>
              </Box>
            )}
          </Card>
        </Grid>
      </Grid>

      {/* New Message Dialog */}
      <Dialog
        open={newMessageDialogOpen}
        onClose={() => setNewMessageDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>New Message</DialogTitle>
        <DialogContent>
          <TextField
            fullWidth
            label="Subject"
            value={newSubject}
            onChange={(e) => setNewSubject(e.target.value)}
            margin="normal"
            variant="outlined"
          />
          <TextField
            fullWidth
            multiline
            rows={6}
            label="Message"
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            margin="normal"
            variant="outlined"
            placeholder="Type your message here..."
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setNewMessageDialogOpen(false)}>
            Cancel
          </Button>
          <Button
            variant="contained"
            onClick={() => sendMessage(newMessage)}
            disabled={!newSubject.trim() || !newMessage.trim()}
          >
            Send Message
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default MessagesPage;