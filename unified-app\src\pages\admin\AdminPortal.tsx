import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { Box } from '@mui/material';

// Layout Components
import AdminLayout from '../../components/layout/AdminLayout';

// Admin Pages
import AdminDashboard from './AdminDashboard';

// Operations Pages
import LiveTrackingPage from './operations/LiveTrackingPage';

// Workforce Pages
import AgentManagementPage from './workforce/AgentManagementPage';

// Sites Pages
import SitesOverviewPage from './sites/SitesOverviewPage';

const AdminPortal: React.FC = () => {
  return (
    <AdminLayout>
      <Box sx={{ flexGrow: 1, p: 3 }}>
        <Routes>
          {/* Default route redirects to dashboard */}
          <Route path="/" element={<Navigate to="/admin/dashboard" replace />} />
          
          {/* Dashboard */}
          <Route path="/dashboard" element={<AdminDashboard />} />
          
          {/* Operations Routes */}
          <Route path="/operations/tracking" element={<div>Live Tracking Page</div>} />
          <Route path="/operations/incidents" element={<div>Incident Response Page</div>} />
          <Route path="/operations/communication" element={<div>Communication Center Page</div>} />
          
          {/* Workforce Routes */}
          <Route path="/workforce/agents" element={<div>Agent Management Page</div>} />
          <Route path="/workforce/performance" element={<div>Performance Tracking Page</div>} />
          <Route path="/workforce/training" element={<div>Training Management Page</div>} />
          <Route path="/workforce/analytics" element={<div>Workforce Analytics Page</div>} />
          <Route path="/workforce/attendance" element={<div>Attendance Management Page</div>} />
          <Route path="/workforce/scheduling" element={<div>Workforce Scheduling Page</div>} />
          
          {/* Sites Routes */}
          <Route path="/sites/overview" element={<div>Sites Overview Page</div>} />
          <Route path="/sites/geofencing" element={<div>Geofencing Management Page</div>} />
          <Route path="/sites/security" element={<div>Site Security Page</div>} />
          
          {/* Clients Routes */}
          <Route path="/clients/overview" element={<div>Client Overview Page</div>} />
          <Route path="/clients/portal" element={<div>Client Portal Page</div>} />
          <Route path="/clients/contracts" element={<div>Contracts Page</div>} />
          <Route path="/clients/billing" element={<div>Billing & Invoicing Page</div>} />
          
          {/* Reports Routes */}
          <Route path="/reports/analytics" element={<div>Analytics Dashboard Page</div>} />
          <Route path="/reports/custom" element={<div>Custom Reports Page</div>} />
          <Route path="/reports/incidents" element={<div>Incident Reports Page</div>} />
          <Route path="/reports/patrol" element={<div>Patrol Reports Page</div>} />
          
          {/* Admin Routes */}
          <Route path="/admin/system" element={<div>System Settings Page</div>} />
          <Route path="/admin/security" element={<div>Security Settings Page</div>} />
          <Route path="/admin/compliance" element={<div>Compliance Management Page</div>} />
          <Route path="/admin/integrations" element={<div>Integrations Page</div>} />
          <Route path="/admin/audit" element={<div>Audit Logs Page</div>} />
          <Route path="/admin/users" element={<div>User Management Page</div>} />
          
          {/* Core Routes */}
          <Route path="/notifications" element={<div>Notifications Center Page</div>} />
          <Route path="/profile" element={<div>Profile Management Page</div>} />
          <Route path="/settings" element={<div>Settings Page</div>} />
          
          {/* Fallback */}
          <Route path="*" element={<Navigate to="/admin/dashboard" replace />} />
        </Routes>
      </Box>
    </AdminLayout>
  );
};

export default AdminPortal;
