apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-config
  namespace: bahinlink
data:
  prometheus.yml: |
    global:
      scrape_interval: 15s
      evaluation_interval: 15s
    
    rule_files:
      - "/etc/prometheus/rules/*.yml"
    
    alerting:
      alertmanagers:
        - static_configs:
            - targets:
              - alertmanager:9093
    
    scrape_configs:
      # Prometheus itself
      - job_name: 'prometheus'
        static_configs:
          - targets: ['localhost:9090']
      
      # Node Exporter
      - job_name: 'node-exporter'
        kubernetes_sd_configs:
          - role: endpoints
        relabel_configs:
          - source_labels: [__meta_kubernetes_endpoints_name]
            regex: node-exporter
            action: keep
      
      # BahinLink API
      - job_name: 'bahinlink-api'
        kubernetes_sd_configs:
          - role: endpoints
            namespaces:
              names:
                - bahinlink
        relabel_configs:
          - source_labels: [__meta_kubernetes_service_name]
            regex: api
            action: keep
          - source_labels: [__meta_kubernetes_endpoint_port_name]
            regex: metrics
            action: keep
      
      # PostgreSQL
      - job_name: 'postgres'
        kubernetes_sd_configs:
          - role: endpoints
            namespaces:
              names:
                - bahinlink
        relabel_configs:
          - source_labels: [__meta_kubernetes_service_name]
            regex: postgres-exporter
            action: keep
      
      # Redis
      - job_name: 'redis'
        kubernetes_sd_configs:
          - role: endpoints
            namespaces:
              names:
                - bahinlink
        relabel_configs:
          - source_labels: [__meta_kubernetes_service_name]
            regex: redis-exporter
            action: keep
      
      # Nginx Ingress
      - job_name: 'nginx-ingress'
        kubernetes_sd_configs:
          - role: pod
            namespaces:
              names:
                - ingress-nginx
        relabel_configs:
          - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
            action: keep
            regex: true
          - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
            action: replace
            target_label: __metrics_path__
            regex: (.+)

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: prometheus
  namespace: bahinlink
  labels:
    app: prometheus
spec:
  replicas: 1
  selector:
    matchLabels:
      app: prometheus
  template:
    metadata:
      labels:
        app: prometheus
    spec:
      serviceAccountName: prometheus
      containers:
      - name: prometheus
        image: prom/prometheus:v2.45.0
        args:
          - '--config.file=/etc/prometheus/prometheus.yml'
          - '--storage.tsdb.path=/prometheus/'
          - '--web.console.libraries=/etc/prometheus/console_libraries'
          - '--web.console.templates=/etc/prometheus/consoles'
          - '--storage.tsdb.retention.time=30d'
          - '--web.enable-lifecycle'
          - '--web.enable-admin-api'
        ports:
        - containerPort: 9090
        volumeMounts:
        - name: prometheus-config
          mountPath: /etc/prometheus/
        - name: prometheus-storage
          mountPath: /prometheus/
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /-/healthy
            port: 9090
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /-/ready
            port: 9090
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: prometheus-config
        configMap:
          name: prometheus-config
      - name: prometheus-storage
        persistentVolumeClaim:
          claimName: prometheus-storage

---
apiVersion: v1
kind: Service
metadata:
  name: prometheus
  namespace: bahinlink
  labels:
    app: prometheus
spec:
  selector:
    app: prometheus
  ports:
  - name: web
    port: 9090
    targetPort: 9090
  type: ClusterIP

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: prometheus-storage
  namespace: bahinlink
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 50Gi
  storageClassName: fast-ssd

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: prometheus
  namespace: bahinlink

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: prometheus
rules:
- apiGroups: [""]
  resources:
  - nodes
  - nodes/proxy
  - services
  - endpoints
  - pods
  verbs: ["get", "list", "watch"]
- apiGroups:
  - extensions
  resources:
  - ingresses
  verbs: ["get", "list", "watch"]
- nonResourceURLs: ["/metrics"]
  verbs: ["get"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: prometheus
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: prometheus
subjects:
- kind: ServiceAccount
  name: prometheus
  namespace: bahinlink

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-rules
  namespace: bahinlink
data:
  bahinlink.yml: |
    groups:
    - name: bahinlink.rules
      rules:
      # API Health
      - alert: APIDown
        expr: up{job="bahinlink-api"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "BahinLink API is down"
          description: "BahinLink API has been down for more than 1 minute."
      
      # High Response Time
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job="bahinlink-api"}[5m])) > 1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High API response time"
          description: "95th percentile response time is {{ $value }}s"
      
      # High Error Rate
      - alert: HighErrorRate
        expr: rate(http_requests_total{job="bahinlink-api",status=~"5.."}[5m]) / rate(http_requests_total{job="bahinlink-api"}[5m]) > 0.05
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "High error rate"
          description: "Error rate is {{ $value | humanizePercentage }}"
      
      # Database Connection Issues
      - alert: DatabaseConnectionHigh
        expr: pg_stat_activity_count{job="postgres"} > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High database connections"
          description: "Database has {{ $value }} active connections"
      
      # Memory Usage
      - alert: HighMemoryUsage
        expr: (container_memory_usage_bytes{pod=~"api-.*"} / container_spec_memory_limit_bytes) > 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage"
          description: "Memory usage is {{ $value | humanizePercentage }}"
      
      # CPU Usage
      - alert: HighCPUUsage
        expr: rate(container_cpu_usage_seconds_total{pod=~"api-.*"}[5m]) > 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage"
          description: "CPU usage is {{ $value | humanizePercentage }}"
      
      # Disk Space
      - alert: LowDiskSpace
        expr: (node_filesystem_avail_bytes{mountpoint="/"} / node_filesystem_size_bytes{mountpoint="/"}) < 0.1
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "Low disk space"
          description: "Disk space is {{ $value | humanizePercentage }} full"
