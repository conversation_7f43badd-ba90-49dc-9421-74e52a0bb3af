# BahinLink Production Environment Configuration
# This file contains production environment variables
# DO NOT commit sensitive values to version control

# Application Environment
NODE_ENV=production
APP_NAME=BahinLink
APP_VERSION=1.0.0
APP_URL=https://app.bahinlink.com
API_URL=https://api.bahinlink.com
WS_URL=wss://ws.bahinlink.com

# Server Configuration
PORT=3000
WS_PORT=3001
HOST=0.0.0.0
CLUSTER_MODE=true
WORKERS=auto

# Database Configuration
DATABASE_URL="prisma+postgres://accelerate.prisma-data.net/?api_key=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhcGlfa2V5IjoiMDFKWTJGRFNRODNSRzQySjNYUVNRUjVZMjIiLCJ0ZW5hbnRfaWQiOiIyYzRiNmMzOWU0YmI3YTYzMDIwMzMwZGI0N2NhNDliMzgzMTY2OTM4MGYzNjIyMjEwM2JiM2M5MmRkZjE3M2Q4IiwiaW50ZXJuYWxfc2VjcmV0IjoiZDAwZTI4YjMtMThjOS00MGI5LThlYTAtYTg4YjEzNmM2ZGJiIn0.nmjxODOf4RnJVbd-Y5q41dyMyEfpk7MGNh7xulQTu9o"
DATABASE_SSL=true
DATABASE_POOL_MIN=5
DATABASE_POOL_MAX=20
DATABASE_TIMEOUT=30000
DATABASE_RETRY_ATTEMPTS=3

# Redis Configuration
REDIS_URL=redis://username:password@redis:6379/0
REDIS_CLUSTER_MODE=false
REDIS_SENTINEL_MODE=false
REDIS_PASSWORD=your_redis_password
REDIS_DB=0
REDIS_CONNECT_TIMEOUT=10000
REDIS_COMMAND_TIMEOUT=5000

# Clerk Authentication Integration (Backend)
CLERK_SECRET_KEY=sk_live_your-production-clerk-secret-key-here
CLERK_WEBHOOK_SECRET=whsec_your-production-clerk-webhook-secret-here
CLERK_PUBLISHABLE_KEY=pk_live_your-production-clerk-publishable-key-here

# Encryption Configuration (for general data encryption, not authentication)
ENCRYPTION_KEY=your_32_character_encryption_key_here
ENCRYPTION_ALGORITHM=aes-256-gcm

# AWS Configuration
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_aws_access_key_id
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key
S3_BUCKET=bahinlink-production-files
S3_REGION=us-east-1
S3_CDN_URL=https://cdn.bahinlink.com

# Firebase Configuration
FIREBASE_PROJECT_ID=bahinlink-production
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_PRIVATE_KEY=your_firebase_private_key_here
FIREBASE_DATABASE_URL=https://bahinlink-production.firebaseio.com

# Email Configuration
SMTP_HOST=smtp.sendgrid.net
SMTP_PORT=587
SMTP_SECURE=true
SMTP_USER=apikey
SMTP_PASS=your_sendgrid_api_key_here
EMAIL_FROM=<EMAIL>
EMAIL_FROM_NAME=BahinLink Security

# Push Notifications
VAPID_PUBLIC_KEY=your_vapid_public_key_here
VAPID_PRIVATE_KEY=your_vapid_private_key_here
VAPID_EMAIL=<EMAIL>
FCM_SERVER_KEY=your_fcm_server_key_here
APNS_KEY_ID=your_apns_key_id_here
APNS_TEAM_ID=your_apns_team_id_here
APNS_PRIVATE_KEY=your_apns_private_key_here

# Google Services
GOOGLE_MAPS_API_KEY=your_google_maps_api_key_here
GOOGLE_PLACES_API_KEY=your_google_places_api_key_here
GOOGLE_GEOCODING_API_KEY=your_google_geocoding_api_key_here

# Monitoring & Logging
LOG_LEVEL=info
LOG_FORMAT=json
LOG_FILE_ENABLED=true
LOG_FILE_PATH=/app/logs/application.log
LOG_MAX_SIZE=100m
LOG_MAX_FILES=10
LOG_COMPRESS=true

# APM Configuration
APM_ENABLED=true
APM_SERVICE_NAME=bahinlink-api
APM_ENVIRONMENT=production
APM_SERVER_URL=https://apm.bahinlink.com
APM_SECRET_TOKEN=your_apm_secret_token_here

# Metrics Configuration
METRICS_ENABLED=true
METRICS_PORT=9090
METRICS_PATH=/metrics
PROMETHEUS_ENABLED=true

# Security Configuration
CORS_ORIGIN=https://app.bahinlink.com,https://admin.bahinlink.com,https://client.bahinlink.com
CORS_CREDENTIALS=true
HELMET_ENABLED=true
RATE_LIMIT_ENABLED=true
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_SKIP_SUCCESSFUL_REQUESTS=false

# Session Configuration
SESSION_SECRET=your_session_secret_here
SESSION_SECURE=true
SESSION_HTTP_ONLY=true
SESSION_SAME_SITE=strict
SESSION_MAX_AGE=86400000

# File Upload Configuration
UPLOAD_MAX_SIZE=50mb
UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/gif,application/pdf,video/mp4,audio/mpeg
UPLOAD_DESTINATION=/app/uploads
UPLOAD_TEMP_DIR=/tmp/uploads

# Cache Configuration
CACHE_TTL=3600
CACHE_MAX_SIZE=1000
CACHE_CHECK_PERIOD=600
CACHE_ENABLED=true

# WebSocket Configuration
WS_HEARTBEAT_INTERVAL=30000
WS_HEARTBEAT_TIMEOUT=60000
WS_MAX_CONNECTIONS=10000
WS_COMPRESSION=true
WS_CORS_ORIGIN=https://app.bahinlink.com,https://admin.bahinlink.com,https://client.bahinlink.com

# Background Jobs
QUEUE_REDIS_URL=redis://username:password@redis:6379/1
QUEUE_CONCURRENCY=5
QUEUE_MAX_ATTEMPTS=3
QUEUE_BACKOFF_TYPE=exponential
QUEUE_BACKOFF_DELAY=2000

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=bahinlink-backups
BACKUP_ENCRYPTION_ENABLED=true

# Health Check Configuration
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_PATH=/health
HEALTH_CHECK_TIMEOUT=5000
READINESS_CHECK_PATH=/ready

# Feature Flags
FEATURE_REAL_TIME_TRACKING=true
FEATURE_EMERGENCY_ALERTS=true
FEATURE_VOICE_MESSAGES=true
FEATURE_VIDEO_CALLS=true
FEATURE_BIOMETRIC_AUTH=true
FEATURE_OFFLINE_MODE=true
FEATURE_ANALYTICS=true
FEATURE_REPORTING=true

# Third-party Integrations
TWILIO_ACCOUNT_SID=your_twilio_account_sid_here
TWILIO_AUTH_TOKEN=your_twilio_auth_token_here
TWILIO_PHONE_NUMBER=+**********

STRIPE_PUBLISHABLE_KEY=pk_live_your_stripe_publishable_key_here
STRIPE_SECRET_KEY=sk_live_your_stripe_secret_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_stripe_webhook_secret_here

# Compliance & Audit
AUDIT_LOG_ENABLED=true
AUDIT_LOG_LEVEL=info
COMPLIANCE_MODE=strict
DATA_RETENTION_DAYS=2555
GDPR_COMPLIANCE=true
HIPAA_COMPLIANCE=false

# Performance Configuration
COMPRESSION_ENABLED=true
COMPRESSION_LEVEL=6
COMPRESSION_THRESHOLD=1024
ETAG_ENABLED=true
STATIC_CACHE_MAX_AGE=********

# SSL/TLS Configuration
SSL_ENABLED=true
SSL_CERT_PATH=/etc/ssl/certs/bahinlink.crt
SSL_KEY_PATH=/etc/ssl/private/bahinlink.key
SSL_CA_PATH=/etc/ssl/certs/ca-bundle.crt
HSTS_ENABLED=true
HSTS_MAX_AGE=********

# Container Configuration
CONTAINER_MEMORY_LIMIT=1g
CONTAINER_CPU_LIMIT=1000m
CONTAINER_RESTART_POLICY=unless-stopped
CONTAINER_HEALTH_CHECK_INTERVAL=30s

# Kubernetes Configuration
K8S_NAMESPACE=bahinlink-production
K8S_SERVICE_ACCOUNT=bahinlink-api
K8S_CLUSTER_NAME=bahinlink-cluster
K8S_INGRESS_CLASS=nginx

# Development Tools (disabled in production)
DEBUG=false
SWAGGER_ENABLED=false
GRAPHQL_PLAYGROUND=false
PROFILER_ENABLED=false

# Client Application URLs
ADMIN_PORTAL_URL=https://admin.bahinlink.com
CLIENT_PORTAL_URL=https://client.bahinlink.com
MOBILE_APP_DEEP_LINK=bahinlink://

# Notification Templates
EMAIL_TEMPLATE_DIR=/app/templates/email
SMS_TEMPLATE_DIR=/app/templates/sms
PUSH_TEMPLATE_DIR=/app/templates/push

# Timezone Configuration
DEFAULT_TIMEZONE=UTC
BUSINESS_TIMEZONE=America/New_York

# API Rate Limiting
API_RATE_LIMIT_ENABLED=true
API_RATE_LIMIT_WINDOW=15
API_RATE_LIMIT_MAX=1000
API_RATE_LIMIT_SKIP_SUCCESS=true

# Database Migrations
AUTO_MIGRATE=false
MIGRATION_TIMEOUT=300000
SEED_DATA=false

# Error Reporting
ERROR_REPORTING_ENABLED=true
SENTRY_DSN=https://your_sentry_dsn_here
SENTRY_ENVIRONMENT=production
SENTRY_RELEASE=1.0.0

# Content Security Policy
CSP_ENABLED=true
CSP_REPORT_ONLY=false
CSP_REPORT_URI=https://api.bahinlink.com/csp-report

# API Documentation
API_DOCS_ENABLED=false
API_DOCS_PATH=/docs
API_DOCS_TITLE=BahinLink API
API_DOCS_VERSION=1.0.0
