import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface ClientAgent {
  id: string;
  name: string;
  status: 'on-duty' | 'off-duty';
  currentSite?: string;
  lastUpdate: string;
}

interface ClientAgentsState {
  agents: ClientAgent[];
  loading: boolean;
  error: string | null;
}

const initialState: ClientAgentsState = {
  agents: [],
  loading: false,
  error: null,
};

const clientAgentsSlice = createSlice({
  name: 'clientAgents',
  initialState,
  reducers: {
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setAgents: (state, action: PayloadAction<ClientAgent[]>) => {
      state.agents = action.payload;
    },
    updateAgent: (state, action: PayloadAction<ClientAgent>) => {
      const index = state.agents.findIndex(agent => agent.id === action.payload.id);
      if (index !== -1) {
        state.agents[index] = action.payload;
      }
    },
    setError: (state, action: PayloadAction<string>) => {
      state.error = action.payload;
      state.loading = false;
    },
  },
});

export const { setLoading, setAgents, updateAgent, setError } = clientAgentsSlice.actions;
export default clientAgentsSlice.reducer;
