import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { Box } from '@mui/material';

// Layout Components
import ClientLayout from '../../components/layout/ClientLayout';

// Client Pages - Placeholder components for now
import ClientDashboard from './ClientDashboard';

const ClientPortal: React.FC = () => {
  return (
    <ClientLayout>
      <Box sx={{ flexGrow: 1, p: 3 }}>
        <Routes>
          {/* Default route redirects to dashboard */}
          <Route path="/" element={<Navigate to="/client/dashboard" replace />} />
          
          {/* Dashboard */}
          <Route path="/dashboard" element={<ClientDashboard />} />
          
          {/* Monitoring */}
          <Route path="/monitoring" element={<div>Live Monitoring Page</div>} />
          
          {/* Reports */}
          <Route path="/reports" element={<div>Reports Page</div>} />
          <Route path="/reports/analytics" element={<div>Analytics Page</div>} />
          <Route path="/reports/performance" element={<div>Performance Page</div>} />
          <Route path="/reports/summary" element={<div>Summary Page</div>} />
          
          {/* Service Requests */}
          <Route path="/service-requests" element={<div>Service Requests Page</div>} />
          
          {/* Incidents */}
          <Route path="/incidents" element={<div>Incidents Page</div>} />
          
          {/* Communication */}
          <Route path="/messages" element={<div>Messages Page</div>} />
          <Route path="/notifications" element={<div>Notifications Page</div>} />
          
          {/* Billing */}
          <Route path="/billing" element={<div>Billing Page</div>} />
          
          {/* Settings & Profile */}
          <Route path="/settings" element={<div>Settings Page</div>} />
          <Route path="/profile" element={<div>Profile Page</div>} />
          
          {/* Fallback */}
          <Route path="*" element={<Navigate to="/client/dashboard" replace />} />
        </Routes>
      </Box>
    </ClientLayout>
  );
};

export default ClientPortal;
