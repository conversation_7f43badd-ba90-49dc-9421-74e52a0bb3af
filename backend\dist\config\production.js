"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const productionConfig = {
    port: parseInt(process.env.PORT || '3000', 10),
    nodeEnv: 'production',
    database: {
        url: process.env.DATABASE_URL || '',
        ssl: process.env.NODE_ENV === 'production',
        retryAttempts: 3,
        pool: {
            min: 2,
            max: 10,
            acquireTimeoutMillis: 30000,
            createTimeoutMillis: 30000,
            destroyTimeoutMillis: 5000,
            idleTimeoutMillis: 30000,
            reapIntervalMillis: 1000,
            createRetryIntervalMillis: 100,
            retryAttempts: 3,
        },
    },
    redis: {
        url: process.env.REDIS_URL || 'redis://localhost:6379',
        password: process.env.REDIS_PASSWORD,
        db: parseInt(process.env.REDIS_DB || '0', 10),
    },
    jwt: {
        secret: process.env.JWT_SECRET || 'your-secret-key',
        expiresIn: '24h',
    },
    cors: {
        origin: process.env.CORS_ORIGIN?.split(',') || ['http://localhost:3000'],
        credentials: true,
    },
    rateLimit: {
        windowMs: 15 * 60 * 1000,
        max: 100,
    },
    monitoring: {
        enabled: process.env.MONITORING_ENABLED === 'true',
        port: parseInt(process.env.MONITORING_PORT || '9090', 10),
        metricsPath: '/metrics',
    },
    logging: {
        level: process.env.LOG_LEVEL || 'info',
        format: 'json',
    },
    security: {
        bcryptRounds: 12,
        sessionTimeout: 24 * 60 * 60 * 1000,
    },
    integrations: {
        twilio: {
            accountSid: process.env.TWILIO_ACCOUNT_SID || '',
            authToken: process.env.TWILIO_AUTH_TOKEN || '',
            phoneNumber: process.env.TWILIO_PHONE_NUMBER || '',
        },
        sendgrid: {
            apiKey: process.env.SENDGRID_API_KEY || '',
            fromEmail: process.env.SENDGRID_FROM_EMAIL || '<EMAIL>',
        },
        firebase: {
            projectId: process.env.FIREBASE_PROJECT_ID || '',
            privateKey: process.env.FIREBASE_PRIVATE_KEY || '',
            clientEmail: process.env.FIREBASE_CLIENT_EMAIL || '',
        },
        aws: {
            accessKeyId: process.env.AWS_ACCESS_KEY_ID || '',
            secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || '',
            region: process.env.AWS_REGION || 'us-east-1',
            s3: {
                bucket: process.env.AWS_S3_BUCKET || '',
            },
        },
    },
};
exports.default = productionConfig;
//# sourceMappingURL=production.js.map