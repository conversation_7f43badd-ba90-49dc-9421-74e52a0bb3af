import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface ClientSite {
  id: string;
  name: string;
  status: 'active' | 'inactive';
  agentsOnDuty: number;
  lastActivity: string;
}

interface ClientSitesState {
  sites: ClientSite[];
  loading: boolean;
  error: string | null;
}

const initialState: ClientSitesState = {
  sites: [],
  loading: false,
  error: null,
};

const clientSitesSlice = createSlice({
  name: 'clientSites',
  initialState,
  reducers: {
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setSites: (state, action: PayloadAction<ClientSite[]>) => {
      state.sites = action.payload;
    },
    updateSite: (state, action: PayloadAction<ClientSite>) => {
      const index = state.sites.findIndex(site => site.id === action.payload.id);
      if (index !== -1) {
        state.sites[index] = action.payload;
      }
    },
    setError: (state, action: PayloadAction<string>) => {
      state.error = action.payload;
      state.loading = false;
    },
  },
});

export const { setLoading, setSites, updateSite, setError } = clientSitesSlice.actions;
export default clientSitesSlice.reducer;
