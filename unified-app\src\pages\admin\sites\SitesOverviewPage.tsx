import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Grid,
  Card,
  CardContent,
  Ty<PERSON><PERSON>,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  Alert,
  CircularProgress,
  Avatar,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  LocationOn as LocationIcon,
  Security as SecurityIcon,
  People as PeopleIcon,
  Business as BusinessIcon,
} from '@mui/icons-material';
import { useAppSelector, useAppDispatch } from '../../../store/hooks';
import { setSites, setLoading, setError } from '../../../store/slices/admin/sitesSlice';

interface Site {
  id: string;
  name: string;
  address: string;
  city: string;
  country: string;
  client: {
    id: string;
    name: string;
    logo?: string;
  };
  status: 'active' | 'inactive' | 'maintenance';
  type: 'bank' | 'retail' | 'office' | 'residential' | 'industrial' | 'other';
  coordinates: {
    lat: number;
    lng: number;
  };
  assignedAgents: number;
  maxAgents: number;
  shiftPattern: '24/7' | 'day_only' | 'night_only' | 'custom';
  securityLevel: 'low' | 'medium' | 'high' | 'critical';
  equipment: string[];
  emergencyContacts: Array<{
    name: string;
    phone: string;
    role: string;
  }>;
  contractStart: string;
  contractEnd: string;
  monthlyValue: number;
}

const SitesOverviewPage: React.FC = () => {
  const dispatch = useAppDispatch();
  const { sites, loading, error } = useAppSelector(state => state.admin.sites);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedSite, setSelectedSite] = useState<Site | null>(null);
  const [dialogMode, setDialogMode] = useState<'add' | 'edit' | 'view'>('add');

  useEffect(() => {
    loadSites();
  }, []);

  const loadSites = async () => {
    dispatch(setLoading(true));
    try {
      // Mock data - in real app, this would be an API call
      const mockSites: Site[] = [
        {
          id: '1',
          name: 'ABC Bank Main Branch',
          address: 'Hamra Street, Beirut',
          city: 'Beirut',
          country: 'Lebanon',
          client: {
            id: 'client1',
            name: 'ABC Bank',
          },
          status: 'active',
          type: 'bank',
          coordinates: { lat: 33.8938, lng: 35.5018 },
          assignedAgents: 3,
          maxAgents: 4,
          shiftPattern: '24/7',
          securityLevel: 'high',
          equipment: ['CCTV', 'Access Control', 'Alarm System', 'Metal Detector'],
          emergencyContacts: [
            { name: 'John Manager', phone: '+961 1 123 456', role: 'Branch Manager' },
            { name: 'Security Desk', phone: '+961 1 123 457', role: 'Security' },
          ],
          contractStart: '2023-01-01',
          contractEnd: '2024-12-31',
          monthlyValue: 15000,
        },
        {
          id: '2',
          name: 'XYZ Shopping Mall',
          address: 'Verdun Area, Beirut',
          city: 'Beirut',
          country: 'Lebanon',
          client: {
            id: 'client2',
            name: 'XYZ Properties',
          },
          status: 'active',
          type: 'retail',
          coordinates: { lat: 33.8547, lng: 35.4856 },
          assignedAgents: 6,
          maxAgents: 8,
          shiftPattern: 'custom',
          securityLevel: 'medium',
          equipment: ['CCTV', 'Patrol Points', 'Emergency Buttons'],
          emergencyContacts: [
            { name: 'Mall Manager', phone: '+961 1 234 567', role: 'General Manager' },
            { name: 'Control Room', phone: '+961 1 234 568', role: 'Security Control' },
          ],
          contractStart: '2023-06-01',
          contractEnd: '2025-05-31',
          monthlyValue: 25000,
        },
        {
          id: '3',
          name: 'Corporate Office Building',
          address: 'Downtown Beirut',
          city: 'Beirut',
          country: 'Lebanon',
          client: {
            id: 'client3',
            name: 'Tech Corp',
          },
          status: 'maintenance',
          type: 'office',
          coordinates: { lat: 33.8869, lng: 35.5131 },
          assignedAgents: 2,
          maxAgents: 3,
          shiftPattern: 'day_only',
          securityLevel: 'medium',
          equipment: ['Access Control', 'CCTV', 'Visitor Management'],
          emergencyContacts: [
            { name: 'Facility Manager', phone: '+961 1 345 678', role: 'Facilities' },
          ],
          contractStart: '2023-03-01',
          contractEnd: '2024-02-29',
          monthlyValue: 8000,
        },
      ];

      dispatch(setSites(mockSites));
    } catch (err) {
      dispatch(setError('Failed to load sites'));
    } finally {
      dispatch(setLoading(false));
    }
  };

  const handleOpenDialog = (mode: 'add' | 'edit' | 'view', site?: Site) => {
    setDialogMode(mode);
    setSelectedSite(site || null);
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedSite(null);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'success';
      case 'inactive': return 'default';
      case 'maintenance': return 'warning';
      default: return 'default';
    }
  };

  const getSecurityLevelColor = (level: string) => {
    switch (level) {
      case 'low': return 'info';
      case 'medium': return 'warning';
      case 'high': return 'error';
      case 'critical': return 'error';
      default: return 'default';
    }
  };

  const getSiteTypeIcon = (type: string) => {
    switch (type) {
      case 'bank': return <BusinessIcon />;
      case 'retail': return <BusinessIcon />;
      case 'office': return <BusinessIcon />;
      default: return <LocationIcon />;
    }
  };

  if (loading && sites.length === 0) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress size={60} />
      </Box>
    );
  }

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
          Sites Overview
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => handleOpenDialog('add')}
        >
          Add New Site
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <LocationIcon sx={{ fontSize: 40, color: 'primary.main', mr: 2 }} />
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                    {sites.filter(s => s.status === 'active').length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Active Sites
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <PeopleIcon sx={{ fontSize: 40, color: 'success.main', mr: 2 }} />
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                    {sites.reduce((sum, site) => sum + site.assignedAgents, 0)}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Deployed Agents
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <SecurityIcon sx={{ fontSize: 40, color: 'warning.main', mr: 2 }} />
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                    {sites.filter(s => s.securityLevel === 'high' || s.securityLevel === 'critical').length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    High Security Sites
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <BusinessIcon sx={{ fontSize: 40, color: 'info.main', mr: 2 }} />
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                    ${sites.reduce((sum, site) => sum + site.monthlyValue, 0).toLocaleString()}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Monthly Revenue
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Sites Table */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            All Sites
          </Typography>
          <TableContainer component={Paper} elevation={0}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Site</TableCell>
                  <TableCell>Client</TableCell>
                  <TableCell>Type</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Security Level</TableCell>
                  <TableCell>Agents</TableCell>
                  <TableCell>Monthly Value</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {sites.map((site) => (
                  <TableRow key={site.id}>
                    <TableCell>
                      <Box display="flex" alignItems="center">
                        <Avatar sx={{ mr: 2, bgcolor: 'primary.light' }}>
                          {getSiteTypeIcon(site.type)}
                        </Avatar>
                        <Box>
                          <Typography variant="subtitle2">
                            {site.name}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {site.address}
                          </Typography>
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell>{site.client.name}</TableCell>
                    <TableCell>
                      <Chip label={site.type} size="small" variant="outlined" />
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={site.status}
                        color={getStatusColor(site.status) as any}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={site.securityLevel}
                        color={getSecurityLevelColor(site.securityLevel) as any}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {site.assignedAgents}/{site.maxAgents}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        ${site.monthlyValue.toLocaleString()}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <IconButton
                        size="small"
                        onClick={() => handleOpenDialog('view', site)}
                      >
                        <ViewIcon />
                      </IconButton>
                      <IconButton
                        size="small"
                        onClick={() => handleOpenDialog('edit', site)}
                      >
                        <EditIcon />
                      </IconButton>
                      <IconButton size="small" color="error">
                        <DeleteIcon />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* Site Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="lg" fullWidth>
        <DialogTitle>
          {dialogMode === 'add' && 'Add New Site'}
          {dialogMode === 'edit' && 'Edit Site'}
          {dialogMode === 'view' && 'Site Details'}
        </DialogTitle>
        <DialogContent>
          {selectedSite && dialogMode === 'view' && (
            <Box>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Typography variant="h6" gutterBottom>
                    Site Information
                  </Typography>
                  <Box mb={2}>
                    <Typography variant="subtitle2">Name</Typography>
                    <Typography>{selectedSite.name}</Typography>
                  </Box>
                  <Box mb={2}>
                    <Typography variant="subtitle2">Address</Typography>
                    <Typography>{selectedSite.address}, {selectedSite.city}</Typography>
                  </Box>
                  <Box mb={2}>
                    <Typography variant="subtitle2">Client</Typography>
                    <Typography>{selectedSite.client.name}</Typography>
                  </Box>
                  <Box mb={2}>
                    <Typography variant="subtitle2">Security Level</Typography>
                    <Chip
                      label={selectedSite.securityLevel}
                      color={getSecurityLevelColor(selectedSite.securityLevel) as any}
                      size="small"
                    />
                  </Box>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="h6" gutterBottom>
                    Operational Details
                  </Typography>
                  <Box mb={2}>
                    <Typography variant="subtitle2">Shift Pattern</Typography>
                    <Typography>{selectedSite.shiftPattern}</Typography>
                  </Box>
                  <Box mb={2}>
                    <Typography variant="subtitle2">Agent Capacity</Typography>
                    <Typography>{selectedSite.assignedAgents}/{selectedSite.maxAgents}</Typography>
                  </Box>
                  <Box mb={2}>
                    <Typography variant="subtitle2">Equipment</Typography>
                    <Box>
                      {selectedSite.equipment.map((item, index) => (
                        <Chip key={index} label={item} size="small" sx={{ mr: 1, mb: 1 }} />
                      ))}
                    </Box>
                  </Box>
                  <Box mb={2}>
                    <Typography variant="subtitle2">Monthly Value</Typography>
                    <Typography>${selectedSite.monthlyValue.toLocaleString()}</Typography>
                  </Box>
                </Grid>
              </Grid>
            </Box>
          )}
          {(dialogMode === 'add' || dialogMode === 'edit') && (
            <Box>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                Site form fields would be implemented here
              </Typography>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>
            {dialogMode === 'view' ? 'Close' : 'Cancel'}
          </Button>
          {dialogMode !== 'view' && (
            <Button variant="contained">
              {dialogMode === 'add' ? 'Add Site' : 'Save Changes'}
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default SitesOverviewPage;
