{"version": 3, "file": "analytics.js", "sourceRoot": "", "sources": ["../../src/routes/analytics.ts"], "names": [], "mappings": ";;;;;AAAA,sDAA8B;AAC9B,2CAA8C;AAC9C,yDAA4D;AAE5D,MAAM,MAAM,GAAG,iBAAO,CAAC,MAAM,EAAE,CAAC;AAChC,MAAM,MAAM,GAAG,IAAI,qBAAY,EAAE,CAAC;AAGlC,MAAM,sBAAsB,GAAG,CAAC,GAAoB,EAAE,GAAqB,EAAE,IAA0B,EAAE,EAAE;IACzG,MAAM,MAAM,GAAG,IAAA,oCAAgB,EAAC,GAAG,CAAC,CAAC;IACrC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE,CAAC;QACtB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAC1B,OAAO,EAAE,KAAK;YACd,KAAK,EAAE;gBACL,IAAI,EAAE,kBAAkB;gBACxB,OAAO,EAAE,oBAAoB;gBAC7B,OAAO,EAAE,MAAM,CAAC,KAAK,EAAE;aACxB;SACF,CAAC,CAAC;IACL,CAAC;IACD,IAAI,EAAE,CAAC;AACT,CAAC,CAAC;AAGF,MAAM,CAAC,GAAG,CAAC,YAAY,EAAE;IACvB,IAAA,yBAAK,EAAC,WAAW,CAAC,CAAC,QAAQ,EAAE,CAAC,SAAS,EAAE;IACzC,IAAA,yBAAK,EAAC,SAAS,CAAC,CAAC,QAAQ,EAAE,CAAC,SAAS,EAAE;IACvC,IAAA,yBAAK,EAAC,UAAU,CAAC,CAAC,QAAQ,EAAE,CAAC,MAAM,EAAE;CACtC,EAAE,sBAAsB,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IAC5C,IAAI,CAAC;QACH,MAAM,SAAS,GAAG,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,SAAmB,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC,OAAO,CAAC,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;QAC1I,MAAM,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,OAAiB,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC;QACvF,MAAM,QAAQ,GAAG,GAAG,CAAC,KAAK,CAAC,QAAkB,CAAC;QAG9C,MAAM,SAAS,GAAQ;YACrB,SAAS,EAAE,IAAI;YACf,SAAS,EAAE;gBACT,GAAG,EAAE,SAAS;gBACd,GAAG,EAAE,OAAO;aACb;SACF,CAAC;QAEF,IAAI,QAAQ,EAAE,CAAC;YACb,SAAS,CAAC,IAAI,GAAG;gBACf,QAAQ,EAAE,QAAQ;aACnB,CAAC;QACJ,CAAC;QAGD,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC;YAC5C,KAAK,EAAE;gBACL,MAAM,EAAE,aAAa;gBACrB,SAAS,EAAE,IAAI;gBACf,GAAG,CAAC,QAAQ,IAAI;oBACd,IAAI,EAAE;wBACJ,QAAQ,EAAE,QAAQ;qBACnB;iBACF,CAAC;aACH;SACF,CAAC,CAAC;QAGH,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC;YAC3C,KAAK,EAAE;gBACL,gBAAgB,EAAE,QAAQ;gBAC1B,SAAS,EAAE,IAAI;aAChB;SACF,CAAC,CAAC;QAGH,MAAM,KAAK,GAAG,IAAI,IAAI,EAAE,CAAC;QACzB,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC3B,MAAM,QAAQ,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC;QACjC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;QAEzC,MAAM,cAAc,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC;YAC/C,KAAK,EAAE;gBACL,IAAI,EAAE,UAAU;gBAChB,SAAS,EAAE;oBACT,GAAG,EAAE,KAAK;oBACV,EAAE,EAAE,QAAQ;iBACb;gBACD,SAAS,EAAE,IAAI;gBACf,GAAG,CAAC,QAAQ,IAAI;oBACd,IAAI,EAAE;wBACJ,QAAQ,EAAE,QAAQ;qBACnB;iBACF,CAAC;aACH;SACF,CAAC,CAAC;QAGH,MAAM,cAAc,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;YAC7C,KAAK,EAAE;gBACL,MAAM,EAAE,QAAQ;gBAChB,SAAS,EAAE,IAAI;gBACf,GAAG,CAAC,QAAQ,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC;aACxC;SACF,CAAC,CAAC;QAGH,MAAM,CAAC,eAAe,EAAE,WAAW,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;YACvD,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC;gBACjB,KAAK,EAAE;oBACL,GAAG,SAAS;oBACZ,MAAM,EAAE,WAAW;iBACpB;aACF,CAAC;YACF,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC;gBACjB,KAAK,EAAE;oBACL,GAAG,SAAS;oBACZ,MAAM,EAAE;wBACN,EAAE,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,SAAS,CAAC;qBAC1C;iBACF;aACF,CAAC;SACH,CAAC,CAAC;QAEH,MAAM,cAAc,GAAG,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,GAAG,WAAW,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAGnF,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC;YAC7C,KAAK,EAAE;gBACL,GAAG,SAAS;gBACZ,IAAI,EAAE,UAAU;gBAChB,MAAM,EAAE;oBACN,EAAE,EAAE,CAAC,UAAU,EAAE,UAAU,CAAC;iBAC7B;aACF;YACD,MAAM,EAAE;gBACN,SAAS,EAAE,IAAI;gBACf,SAAS,EAAE,IAAI;aAChB;SACF,CAAC,CAAC;QAEH,MAAM,YAAY,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC;YACvC,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,QAAQ,EAAE,EAAE;gBACjC,MAAM,IAAI,GAAG,QAAQ,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,QAAQ,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;gBACzE,OAAO,GAAG,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC;YACpC,CAAC,EAAE,CAAC,CAAC,GAAG,SAAS,CAAC,MAAM;YAC1B,CAAC,CAAC,CAAC,CAAC;QAGN,MAAM,WAAW,GAAG,MAAM,OAAO,CAAC,GAAG,CACnC,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE;YACvC,MAAM,IAAI,GAAG,IAAI,IAAI,EAAE,CAAC;YACxB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;YACjC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAC1B,MAAM,QAAQ,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC;YAChC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;YAEzC,MAAM,KAAK,GAAG,MAAM,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC;gBACrC,KAAK,EAAE;oBACL,SAAS,EAAE;wBACT,GAAG,EAAE,IAAI;wBACT,EAAE,EAAE,QAAQ;qBACb;oBACD,SAAS,EAAE,IAAI;oBACf,GAAG,CAAC,QAAQ,IAAI;wBACd,IAAI,EAAE;4BACJ,QAAQ,EAAE,QAAQ;yBACnB;qBACF,CAAC;iBACH;aACF,CAAC,CAAC;YAEH,OAAO;gBACL,IAAI,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBACtC,MAAM,EAAE,KAAK;aACd,CAAC;QACJ,CAAC,CAAC,CACH,CAAC;QAGF,MAAM,mBAAmB,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC;YACtD,EAAE,EAAE,CAAC,UAAU,CAAC;YAChB,KAAK,EAAE;gBACL,GAAG,SAAS;gBACZ,IAAI,EAAE,UAAU;aACjB;YACD,MAAM,EAAE;gBACN,QAAQ,EAAE,IAAI;aACf;SACF,CAAC,CAAC;QAGH,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC;YAC5C,KAAK,EAAE;gBACL,gBAAgB,EAAE,QAAQ;gBAC1B,SAAS,EAAE,IAAI;aAChB;YACD,OAAO,EAAE;gBACP,IAAI,EAAE;oBACJ,MAAM,EAAE;wBACN,SAAS,EAAE,IAAI;wBACf,QAAQ,EAAE,IAAI;qBACf;iBACF;gBACD,MAAM,EAAE;oBACN,KAAK,EAAE;wBACL,GAAG,SAAS;wBACZ,MAAM,EAAE,WAAW;qBACpB;iBACF;gBACD,MAAM,EAAE;oBACN,MAAM,EAAE;wBACN,MAAM,EAAE;4BACN,KAAK,EAAE;gCACL,GAAG,SAAS;gCACZ,MAAM,EAAE,WAAW;6BACpB;yBACF;qBACF;iBACF;aACF;YACD,OAAO,EAAE;gBACP,MAAM,EAAE;oBACN,MAAM,EAAE,MAAM;iBACf;aACF;YACD,IAAI,EAAE,CAAC;SACR,CAAC,CAAC;QAGH,MAAM,gBAAgB,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC;YACpD,KAAK,EAAE;gBACL,SAAS,EAAE,IAAI;gBACf,SAAS,EAAE;oBACT,GAAG,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;iBAChD;gBACD,GAAG,CAAC,QAAQ,IAAI;oBACd,IAAI,EAAE;wBACJ,QAAQ,EAAE,QAAQ;qBACnB;iBACF,CAAC;aACH;YACD,OAAO,EAAE;gBACP,KAAK,EAAE;oBACL,MAAM,EAAE;wBACN,IAAI,EAAE;4BACJ,MAAM,EAAE;gCACN,SAAS,EAAE,IAAI;gCACf,QAAQ,EAAE,IAAI;6BACf;yBACF;qBACF;iBACF;gBACD,IAAI,EAAE;oBACJ,MAAM,EAAE;wBACN,IAAI,EAAE,IAAI;qBACX;iBACF;aACF;YACD,OAAO,EAAE;gBACP,SAAS,EAAE,MAAM;aAClB;YACD,IAAI,EAAE,EAAE;SACT,CAAC,CAAC;QAEH,MAAM,aAAa,GAAG;YACpB,QAAQ,EAAE;gBACR,YAAY;gBACZ,WAAW;gBACX,cAAc;gBACd,cAAc;gBACd,cAAc,EAAE,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG,GAAG,CAAC,GAAG,GAAG;gBACtD,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,YAAY,GAAG,GAAG,CAAC,GAAG,GAAG;aACnD;YACD,MAAM,EAAE;gBACN,MAAM,EAAE,WAAW,CAAC,OAAO,EAAE;gBAC7B,mBAAmB,EAAE,mBAAmB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;oBACpD,QAAQ,EAAE,IAAI,CAAC,QAAQ;oBACvB,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ;iBAC5B,CAAC,CAAC;aACJ;YACD,SAAS,EAAE,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;gBACjC,EAAE,EAAE,KAAK,CAAC,EAAE;gBACZ,IAAI,EAAE,GAAG,KAAK,CAAC,IAAI,CAAC,SAAS,IAAI,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE;gBACtD,eAAe,EAAE,KAAK,CAAC,MAAM,CAAC,MAAM;gBACpC,UAAU,EAAE,KAAK,CAAC,UAAU;aAC7B,CAAC,CAAC;YACH,gBAAgB,EAAE,gBAAgB,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;gBAClD,EAAE,EAAE,QAAQ,CAAC,EAAE;gBACf,IAAI,EAAE,QAAQ,CAAC,IAAI;gBACnB,KAAK,EAAE,QAAQ,CAAC,KAAK;gBACrB,QAAQ,EAAE,QAAQ,CAAC,QAAQ;gBAC3B,SAAS,EAAE,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,IAAI,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE;gBAC7E,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,IAAI;gBAC5B,SAAS,EAAE,QAAQ,CAAC,SAAS;aAC9B,CAAC,CAAC;SACJ,CAAC;QAEF,GAAG,CAAC,IAAI,CAAC;YACP,OAAO,EAAE,IAAI;YACb,IAAI,EAAE,aAAa;YACnB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;QAC5D,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,KAAK,EAAE;gBACL,IAAI,EAAE,uBAAuB;gBAC7B,OAAO,EAAE,qCAAqC;aAC/C;SACF,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC,CAAC;AAGH,MAAM,CAAC,GAAG,CAAC,cAAc,EAAE;IACzB,IAAA,yBAAK,EAAC,WAAW,CAAC,CAAC,QAAQ,EAAE,CAAC,SAAS,EAAE;IACzC,IAAA,yBAAK,EAAC,SAAS,CAAC,CAAC,QAAQ,EAAE,CAAC,SAAS,EAAE;IACvC,IAAA,yBAAK,EAAC,SAAS,CAAC,CAAC,QAAQ,EAAE,CAAC,MAAM,EAAE;IACpC,IAAA,yBAAK,EAAC,QAAQ,CAAC,CAAC,QAAQ,EAAE,CAAC,MAAM,EAAE;CACpC,EAAE,sBAAsB,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IAC5C,IAAI,CAAC;QACH,MAAM,SAAS,GAAG,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,SAAmB,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC,OAAO,CAAC,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;QAC1I,MAAM,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,OAAiB,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC;QACvF,MAAM,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC,OAAiB,CAAC;QAC5C,MAAM,MAAM,GAAG,GAAG,CAAC,KAAK,CAAC,MAAgB,CAAC;QAE1C,MAAM,SAAS,GAAQ;YACrB,SAAS,EAAE,IAAI;YACf,SAAS,EAAE;gBACT,GAAG,EAAE,SAAS;gBACd,GAAG,EAAE,OAAO;aACb;SACF,CAAC;QAEF,IAAI,OAAO;YAAE,SAAS,CAAC,OAAO,GAAG,OAAO,CAAC;QACzC,IAAI,MAAM;YAAE,SAAS,CAAC,MAAM,GAAG,MAAM,CAAC;QAGtC,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC;YAC9C,EAAE,EAAE,CAAC,QAAQ,CAAC;YACd,KAAK,EAAE,SAAS;YAChB,MAAM,EAAE;gBACN,MAAM,EAAE,IAAI;aACb;SACF,CAAC,CAAC;QAGH,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC;YAChD,EAAE,EAAE,CAAC,MAAM,EAAE,QAAQ,CAAC;YACtB,KAAK,EAAE,SAAS;YAChB,MAAM,EAAE;gBACN,IAAI,EAAE,IAAI;aACX;SACF,CAAC,CAAC;QAGH,MAAM,iBAAiB,GAAG,MAAM,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC;YAC9D,EAAE,EAAE,CAAC,QAAQ,CAAC;YACd,KAAK,EAAE;gBACL,SAAS,EAAE,IAAI;gBACf,SAAS,EAAE;oBACT,GAAG,EAAE,SAAS;oBACd,GAAG,EAAE,OAAO;iBACb;gBACD,GAAG,CAAC,OAAO,IAAI,EAAE,OAAO,EAAE,CAAC;gBAC3B,GAAG,CAAC,MAAM,IAAI,EAAE,MAAM,EAAE,CAAC;aAC1B;YACD,MAAM,EAAE;gBACN,MAAM,EAAE,IAAI;aACb;SACF,CAAC,CAAC;QAEH,GAAG,CAAC,IAAI,CAAC;YACP,OAAO,EAAE,IAAI;YACb,IAAI,EAAE;gBACJ,MAAM,EAAE,YAAY;gBACpB,OAAO,EAAE,aAAa;gBACtB,UAAU,EAAE,iBAAiB;gBAC7B,MAAM,EAAE;oBACN,SAAS;oBACT,OAAO;iBACR;aACF;SACF,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;QAC5D,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,KAAK,EAAE;gBACL,IAAI,EAAE,uBAAuB;gBAC7B,OAAO,EAAE,qCAAqC;aAC/C;SACF,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC,CAAC;AAEH,kBAAe,MAAM,CAAC"}