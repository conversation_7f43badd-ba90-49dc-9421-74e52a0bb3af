import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface Shift {
  id: string;
  agentId: string;
  siteId: string;
  startTime: string;
  endTime: string;
  status: 'scheduled' | 'active' | 'completed' | 'cancelled';
}

interface ShiftsState {
  shifts: Shift[];
  loading: boolean;
  error: string | null;
}

const initialState: ShiftsState = {
  shifts: [],
  loading: false,
  error: null,
};

const shiftsSlice = createSlice({
  name: 'adminShifts',
  initialState,
  reducers: {
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setShifts: (state, action: PayloadAction<Shift[]>) => {
      state.shifts = action.payload;
    },
    addShift: (state, action: PayloadAction<Shift>) => {
      state.shifts.push(action.payload);
    },
    updateShift: (state, action: PayloadAction<Shift>) => {
      const index = state.shifts.findIndex(shift => shift.id === action.payload.id);
      if (index !== -1) {
        state.shifts[index] = action.payload;
      }
    },
    setError: (state, action: PayloadAction<string>) => {
      state.error = action.payload;
      state.loading = false;
    },
  },
});

export const { setLoading, setShifts, addShift, updateShift, setError } = shiftsSlice.actions;
export default shiftsSlice.reducer;
