import React, { useEffect } from 'react';
import { useAuth, useUser } from '@clerk/clerk-react';
import { Box, CircularProgress, Typography } from '@mui/material';

const ClientRedirect: React.FC = () => {
  const { isSignedIn, isLoaded } = useAuth();
  const { user } = useUser();

  useEffect(() => {
    if (isLoaded) {
      if (isSignedIn && user) {
        // Check if user has client role (you can customize this logic)
        const userRole = user.publicMetadata?.role || 'user';
        
        if (userRole === 'client' || userRole === 'user' || userRole === 'CLIENT' || !userRole) {
          // Redirect to client portal with the current path
          const currentPath = window.location.pathname.replace('/client', '');
          const clientUrl = process.env.REACT_APP_CLIENT_PORTAL_URL || 'http://localhost:3002';
          window.location.href = `${clientUrl}${currentPath || '/dashboard'}`;
        } else {
          // Redirect to client login if not client
          window.location.href = '/client/login';
        }
      } else {
        // Redirect to client login if not signed in
        window.location.href = '/client/login';
      }
    }
  }, [isLoaded, isSignedIn, user]);

  if (!isLoaded) {
    return (
      <Box
        display="flex"
        flexDirection="column"
        alignItems="center"
        justifyContent="center"
        minHeight="100vh"
        gap={2}
      >
        <CircularProgress size={60} />
        <Typography variant="h6" color="textSecondary">
          Loading Client Portal...
        </Typography>
      </Box>
    );
  }

  return (
    <Box
      display="flex"
      flexDirection="column"
      alignItems="center"
      justifyContent="center"
      minHeight="100vh"
      gap={2}
    >
      <CircularProgress size={60} />
      <Typography variant="h6" color="textSecondary">
        Redirecting to Client Portal...
      </Typography>
    </Box>
  );
};

export default ClientRedirect;
